{"schemes": ["http"], "swagger": "2.0", "info": {"title": "Swagger Specification 2.0. Host: apiservices-rgr.usmnpz.com.au", "version": "2.0"}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1", "paths": {"/accounts/super/{param1}/salary-history": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}}}}}, "/accounts/super/{param1}/service-history": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}}}}}, "/accounts/{param1}/transactions": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}}}}}, "/agents/chat-availability": {"get": {"parameters": [{"type": "string", "name": "queueIds", "in": "query"}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "string", "name": "X-Requested-With", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/846662735_body"}}}}}, "/agents/chat-availability-v2": {"get": {"parameters": [{"type": "string", "name": "emergencyGroupId", "in": "query"}, {"type": "string", "name": "scheduleGroupId", "in": "query"}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/document/insert": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Originatingsystem", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/document/member-correspondence": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Originatingsystem", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/2768198023_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/agent": {"get": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_body"}}}], "responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/alias": {"get": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"type": "integer", "format": "int16", "name": "X-Identifier", "in": "header", "required": true}, {"type": "string", "name": "X-Identifier", "in": "header", "required": true}, {"type": "string", "name": "X-Identifierscheme", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/files-delete": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_body"}}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/process-files": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/1747697982_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/searchmemberaccount": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/*********_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/eol-api/employer/{param1}/account/{param2}/variance": {"put": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "param2", "in": "path", "required": true}, {"type": "string", "name": "X-Businesstransactionid", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "param2", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/account/{param2}/variance/history": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "param2", "in": "path", "required": true}, {"type": "string", "name": "varianceHistoryFileName", "in": "query"}, {"type": "string", "name": "endDate", "in": "query"}, {"type": "string", "name": "startDate", "in": "query"}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "param2", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/details": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/files": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/4286212836_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/eol-api/employer/{param1}/member-search": {"post": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/4263015308_body"}}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/*********_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/variance/dashboard": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/3185483557_body"}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/variance/summary": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/{param1}/exceptions/missing-mandatory-details/summary": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/memberinteraction/bi-datafeed/trigger-functions": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Action-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Activity-Vector", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Client-Request-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Client-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Correlation-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Execution-Location", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Operation-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Resourcegroup-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Run-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Run-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Subscription-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-System-Id", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Ms-Workflow-Version", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"202": {"description": " ", "schema": {"$ref": "#/definitions/1169730129_body"}}}}}, "/members/accounts": {"get": {"responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/**********_body"}}}}}}, "/members/alias": {"get": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "integer", "format": "int32", "name": "X-Identifier", "in": "header", "required": true}, {"type": "string", "name": "X-Identifierscheme", "in": "header", "required": true}, {"type": "integer", "format": "int32", "name": "X-Membernumber", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/470469948_body"}}}}}, "/members/interactions": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/*********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/contact/addresses": {"put": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Identifierscheme", "in": "header", "required": true}, {"type": "integer", "format": "int32", "name": "X-Membernumber", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/contact/phones": {"put": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/accounts": {"get": {"parameters": [{"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/accounts/{param1}/beneficiaries": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/bpay-details": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/contributions-and-caps": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/dashboard": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/accounts/{param1}/insurance": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/accounts/{param1}/investment-allocations": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/more": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/accounts/{param1}/notices": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/**********_body"}}}}}}, "/mobile-api/accounts/{param1}/pension-payments": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/personal-details": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}}}}}, "/mobile-api/accounts/{param1}/statements": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}}}}}, "/mobile-api/accounts/{param1}/transaction-summary": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/accounts/{param1}/transactions-activity": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/content/retrieve": {"get": {"parameters": [{"type": "string", "name": "item", "in": "query"}, {"type": "string", "name": "X-App-Os", "in": "header"}, {"type": "string", "name": "X-App-Version", "in": "header"}, {"type": "string", "name": "X-Correlationid", "in": "header"}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/content/{param1}/retrieve": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "item", "in": "query"}, {"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/content/{param1}/select-account": {"post": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/investment/{param1}/performance/{param2}": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "param2", "in": "path", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/mobile-api/status": {"get": {"parameters": [{"type": "string", "name": "X-App-Os", "in": "header"}, {"type": "string", "name": "X-App-Version", "in": "header"}, {"type": "string", "name": "X-Correlationid", "in": "header"}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/user/decouple": {"post": {"parameters": [{"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mobile-api/user/logout": {"post": {"parameters": [{"type": "string", "name": "X-App-Os", "in": "header", "required": true}, {"type": "string", "name": "X-App-Version", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/1548566942_body"}, "headers": {"X-Xss-Protection": {"type": "integer", "format": "int8"}}}}}}, "/mol-api/account/create-status": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "string", "name": "X-Referenceid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/account/personal": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/accounts": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/accounts/{param1}/attribute": {"post": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/balance-breakdown": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/accounts/{param1}/balance/history": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "fromDate", "in": "query"}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/benefits-nominations": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/communication-preferences": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/high-security-codes": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/insurance-cover": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/investment/details": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/investment/switch-history": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/accounts/{param1}/transactions": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "array", "items": {}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/accounts/{param1}/transition-cover": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/employer/{param1}": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/ev/{param1}/confirm-verification": {"post": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "integer", "format": "int32", "name": "X-Businesstransactionid", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/2162206317_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/ev/{param1}/verification-details": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/identity": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Referenceid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/identity/login-history": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "integer", "format": "int32", "name": "X-Username", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/696937117_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/identity/mfa/factors": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/1378009835_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/identity/mfa/initiate": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/identity/validate-registration": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/1678467985_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/identity/validate-username": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "string", "name": "X-Username", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"type": "boolean"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/insurance/premium": {"get": {"parameters": [{"type": "string", "name": "occupationRating", "in": "query"}, {"type": "string", "name": "sexAtBirth", "in": "query"}, {"type": "string", "name": "ageNextBirthday", "in": "query"}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/**********_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/investment/allocations": {"get": {"parameters": [{"type": "string", "name": "ids", "in": "query"}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/581687286_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}}, "/mol-api/investment/returns": {"get": {"parameters": [{"type": "string", "name": "isPreTax", "in": "query"}, {"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/**********_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/prospect": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/status": {"get": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/utility/address/complete": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/*********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/utility/address/search": {"post": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/**********_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/utility/titles": {"get": {"parameters": [{"type": "string", "name": "X-Antiforgery-Token", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/380560423_body"}}, "headers": {"X-Content-Type-Options": {"type": "string"}, "X-Xss-Protection": {"type": "string"}}}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/partner-onboarding/application": {"post": {"parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/3813960096_body"}}], "responses": {"202": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/partner-onboarding/insurance/rates": {"get": {"parameters": [{"type": "string", "name": "occupationRating", "in": "query"}, {"type": "string", "name": "sexAtBirth", "in": "query"}, {"type": "string", "name": "age", "in": "query"}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/********_body"}}}}}}, "/partner-onboarding/members/anonymous-search": {"post": {"parameters": [{"type": "string", "name": "X-<PERSON><PERSON>-Status", "in": "header"}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-Proto", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"400": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/1927603818_body"}}}}}}, "/partner-onboarding/members/anonymous-search/": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-For", "in": "header", "required": true}, {"type": "string", "name": "X-Forwarded-Proto", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/*********_body"}}], "responses": {"400": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/1594014721_body"}}}}}}, "/partner-onboarding/members/search": {"post": {"parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/3625279484_body"}}}}}, "/salesforce-crm/appointments": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"401": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/salesforce-crm/appointments/08p9r000000mAebAAE": {"put": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/*********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/salesforce-crm/appointments/reminders/send": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/61374470_body"}}], "responses": {"401": {"description": " ", "schema": {"$ref": "#/definitions/*********_body"}}}}}, "/salesforce-crm/appointments/{param1}": {"put": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/**********_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/salesforce-event-processor/notifications/trigger": {"post": {"parameters": [{"type": "string", "name": "idCounter", "in": "query"}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Action-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Activity-Vector", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Client-Request-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Client-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Correlation-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Execution-Location", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Operation-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Resourcegroup-Name", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Run-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Run-Tracking-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-Subscription-Id", "in": "header", "required": true}, {"type": "string", "name": "X-Ms-Workflow-System-Id", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Ms-Workflow-Version", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/*********_body"}}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/**********_body"}}}}}, "/vercellogprocessor/vercel/VercelWebhookPayload": {"post": {"parameters": [{"type": "string", "name": "X-Api-Key", "in": "header", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "string", "name": "X-Vercel-Signature", "in": "header", "required": true}, {"type": "string", "name": "X-Zeit-Signature", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3050441699_body"}}}], "responses": {"default": {"description": "Response body is empty"}}}}}, "definitions": {"**********_questions": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->questions->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["id"], "properties": {"id": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/user/decouple,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["statusDescription", "statusCode"], "properties": {"statusCode": {"type": "number", "format": "float64"}, "statusDescription": {}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["employmentHistory", "salaryHistory", "serviceHistory", "changeLogDetails", "varianceHistory", "contributionHistory"], "properties": {"changeLogDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_changeLogDetails"}}, "contributionHistory": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_contributionHistory"}}, "employmentHistory": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_employmentHistory"}}, "salaryHistory": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_salaryHistory"}}, "serviceHistory": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_serviceHistory"}}, "varianceHistory": {"$ref": "#/definitions/**********_varianceHistory"}}}, "**********_contactDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->contactDetails . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["residentialAddress", "residentialSameAsPostal", "mobileNumber", "email", "postalAddress"], "properties": {"email": {"type": "string"}, "mobileNumber": {"type": "string"}, "postalAddress": {"$ref": "#/definitions/**********_postalAddress"}, "residentialAddress": {"$ref": "#/definitions/**********_residentialAddress"}, "residentialSameAsPostal": {"type": "boolean"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["optionTitle", "optionId", "performanceData", "investmentFee", "webUrl", "latestReturn"], "properties": {"investmentFee": {"$ref": "#/definitions/618804654_investmentFee"}, "latestReturn": {"$ref": "#/definitions/*********_latestReturn"}, "optionId": {"type": "number", "format": "float64"}, "optionTitle": {"type": "string"}, "performanceData": {"$ref": "#/definitions/1500189963_performanceData"}, "webUrl": {"type": "string"}}}, "1145118137_death": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails->death . 1145118137 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"coverAmount": {}, "units": {}}}, "1169730129_body": {"description": "Definition for RESPONSE CODE:202,CONTENTTYPE:application/json,ENDPOINT:/v1/memberinteraction/bi-datafeed/trigger-functions,METHOD:POST,PARAMETER:http-rsp-body . 1169730129 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["statusQueryGetUri", "restartPostUri", "id", "resumePostUri", "suspendPostUri", "purgeHistoryDeleteUri", "terminatePostUri", "sendEventPostUri"], "properties": {"id": {"type": "string"}, "purgeHistoryDeleteUri": {"type": "string"}, "restartPostUri": {"type": "string"}, "resumePostUri": {"type": "string"}, "sendEventPostUri": {"type": "string"}, "statusQueryGetUri": {"type": "string"}, "suspendPostUri": {"type": "string"}, "terminatePostUri": {"type": "string"}}}, "**********_deathBenefit": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->deathBenefit . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["totalBenefitAmount", "totalBenefitText", "isCoverPresent", "lineItems"], "properties": {"isCoverPresent": {"type": "boolean"}, "lineItems": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_lineItems"}}, "totalBenefitAmount": {"type": "number", "format": "float64"}, "totalBenefitText": {"type": "string"}}}, "**********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->content . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/employer/*,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["employerCode", "employerName", "employerContactNumber", "employerABN"], "properties": {"employerABN": {"type": "string"}, "employerCode": {"type": "string"}, "employerContactNumber": {"type": "string"}, "employerName": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/attribute,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["attributeName", "attributeCategory", "attributeValues"], "properties": {"attributeCategory": {"type": "string"}, "attributeName": {"type": "string"}, "attributeValues": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}}}, "**********_phones": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/*/contact/phones,METHOD:PUT,PARAMETER:http-req-body->phones->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["usageCode", "area", "phone"], "properties": {"area": {"type": "string"}, "phone": {"type": "string"}, "usageCode": {"type": "string"}}}, "**********_allocations": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/investment-allocations,METHOD:GET,PARAMETER:http-rsp-body->allocations->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["assetCode", "assetName", "amount", "percentage", "investmentOptionWebUrl"], "properties": {"amount": {"type": "number", "format": "float64"}, "assetCode": {"type": "string"}, "assetName": {"type": "string"}, "investmentOptionWebUrl": {"type": "string"}, "percentage": {"type": "number", "format": "float64"}}}, "**********_balanceBreakdown": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->estimatedBalance->balanceBreakdown->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["componentName", "balanceAmount"], "properties": {"balanceAmount": {"type": "number", "format": "float64"}, "componentName": {"type": "string"}}}, "**********_memberIdentifier": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/interactions,METHOD:POST,PARAMETER:http-req-body->memberIdentifier->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"identifier": {"type": "string"}}}, "**********_personalContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/bpay-details,METHOD:GET,PARAMETER:http-rsp-body->personalContributions . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["customerReferenceNumber", "billerCode"], "properties": {"billerCode": {"type": "string"}, "customerReferenceNumber": {"type": "string"}}}, "**********_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->label . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->hint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "1378009835_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/mfa/factors,METHOD:GET,PARAMETER:http-rsp-body->>* . 1378009835 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["lastUpdated", "description", "factorType", "factorId"], "properties": {"description": {}, "factorId": {"type": "string"}, "factorType": {"type": "string"}, "lastUpdated": {"type": "string"}}}, "1389509455_pageTitle": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->fields->pageTitle . 1389509455 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "1405824334_addresses": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/*/contact/addresses,METHOD:PUT,PARAMETER:http-req-body->addresses->>* . 1405824334 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["countryName", "locality", "postcode", "stateOrTerritory", "usageCode", "addressLine1", "suburb", "countryIso3Code"], "properties": {"addressLine1": {"type": "string"}, "countryIso3Code": {"type": "string"}, "countryName": {"type": "string"}, "locality": {"type": "string"}, "postcode": {"type": "string"}, "stateOrTerritory": {"type": "string"}, "suburb": {"type": "string"}, "usageCode": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transaction-summary,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["transactionSummaries", "hasStatements"], "properties": {"hasStatements": {"type": "boolean"}, "transactionSummaries": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_transactionSummaries"}}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/notices,METHOD:GET,PARAMETER:http-rsp-body->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["documentName", "documentIdentifier", "documentCreatedDate"], "properties": {"documentCreatedDate": {"type": "string"}, "documentIdentifier": {"type": "string"}, "documentName": {"type": "string"}}}, "**********_addressDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/ev/*/verification-details,METHOD:GET,PARAMETER:http-rsp-body->addressDetails . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "1500189963_performanceData": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->performanceData . 1500189963 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["fiveYearPerformance", "threeYearPerformance", "fytdPerformance", "tenYearPerformance"], "properties": {"fiveYearPerformance": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/561202187_fiveYearPerformance"}}, "fytdPerformance": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_fytdPerformance"}}, "tenYearPerformance": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_tenYearPerformance"}}, "threeYearPerformance": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_threeYearPerformance"}}}}, "**********_incomePayment": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/pension-payments,METHOD:GET,PARAMETER:http-rsp-body->incomePayment . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["annualIncome", "instalmentPaymentAmount", "paymentFrequency", "drawdown<PERSON><PERSON><PERSON>", "paymentsRemaining", "paymentsReceived"], "properties": {"annualIncome": {"type": "number", "format": "float64"}, "drawdownMethod": {"type": "string"}, "instalmentPaymentAmount": {"type": "number", "format": "float64"}, "paymentFrequency": {"type": "string"}, "paymentsReceived": {"type": "number", "format": "float64"}, "paymentsRemaining": {"type": "number", "format": "float64"}}}, "**********_headingLabel": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingLabel . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "1548566942_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/user/logout,METHOD:POST,PARAMETER:http-rsp-body . 1548566942 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["statusDescription", "statusCode"], "properties": {"statusCode": {"type": "number", "format": "float64"}, "statusDescription": {}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["activationUrl", "token"], "properties": {"activationUrl": {"type": "string"}, "token": {"type": "string"}}}, "*********_errorDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->contributionHistory->>*->errorDetails->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["exceptionType", "errorDescription", "fileReference"], "properties": {"errorDescription": {"type": "string"}, "exceptionType": {"type": "string"}, "fileReference": {"type": "string"}}}, "**********_subHeading": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"content": {"$ref": "#/definitions/*********_content"}, "hint": {"$ref": "#/definitions/**********_hint"}, "label": {"$ref": "#/definitions/2899895377_label"}}}, "1589216311_ip": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails->ip . 1589216311 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"coverAmount": {"type": "number", "format": "float64"}, "units": {"type": "number", "format": "float64"}}}, "1594014721_body": {"description": "Definition for RESPONSE CODE:400,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/anonymous-search/,METHOD:POST,PARAMETER:http-rsp-body->>* . 1594014721 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"Code": {"type": "string"}, "Message": {"type": "string"}, "code": {}, "message": {"type": "string"}}}, "1598719277_personalDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->personalDetails . 1598719277 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["sexAtBirth", "dateOfBirth", "surname", "middleName", "<PERSON><PERSON><PERSON>", "gender", "title", "taxFileNumber"], "properties": {"dateOfBirth": {"type": "string"}, "gender": {"type": "string"}, "givenName": {"type": "string"}, "middleName": {"type": "string"}, "sexAtBirth": {"type": "string"}, "surname": {"type": "string"}, "taxFileNumber": {"type": "string"}, "title": {"type": "string"}}}, "**********_personalDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->personalDetails . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["givenNames", "title", "taxFileNumber", "sexAtBirth", "dob", "surname"], "properties": {"dob": {"type": "string"}, "givenNames": {"type": "string"}, "sexAtBirth": {"type": "string"}, "surname": {"type": "string"}, "taxFileNumber": {"type": "string"}, "title": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/files-delete,METHOD:POST,PARAMETER:http-req-body->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["fileMetadataId", "employerNumber"], "properties": {"employerNumber": {"type": "string"}, "fileMetadataId": {"type": "number", "format": "float64"}}}, "**********_balanceSnapshots": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->balanceOverTime->balanceSnapshots->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["investmentComponents", "balanceTotal", "balanceEffectiveDate"], "properties": {"balanceEffectiveDate": {"type": "string"}, "balanceTotal": {"type": "number", "format": "float64"}, "investmentComponents": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_investmentComponents"}}}}, "**********_personDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/personal-details,METHOD:GET,PARAMETER:http-rsp-body->personDetails . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["title", "sexAtBirth", "gender", "tfnStatus", "dob", "lastName", "firstName"], "properties": {"dob": {"type": "string"}, "firstName": {"type": "string"}, "gender": {"type": "string"}, "lastName": {"type": "string"}, "sexAtBirth": {"type": "string"}, "tfnStatus": {"type": "string"}, "title": {"type": "string"}}}, "**********_rollover": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->rollover . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["requested"], "properties": {"requested": {"type": "boolean"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/beneficiaries,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["nominatedBeneficiaries", "nominationEligibility"], "properties": {"nominatedBeneficiaries": {}, "nominationEligibility": {"type": "boolean"}}}, "**********_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"__Updated": {"$ref": "#/definitions/**********___Updated"}, "dismissible": {"$ref": "#/definitions/3867269308_dismissible"}, "heading": {"$ref": "#/definitions/**********_heading"}, "text": {"$ref": "#/definitions/**********_text"}, "type": {"$ref": "#/definitions/*********_type"}}}, "1678467985_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/validate-registration,METHOD:POST,PARAMETER:http-req-body . 1678467985 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["validateRegistrationRequest"], "properties": {"validateRegistrationRequest": {"$ref": "#/definitions/1850111827_validateRegistrationRequest"}}}, "1693306878_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->label . 1693306878 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "1746819597___Updated": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->__Updated . 1746819597 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "1747697982_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/process-files,METHOD:POST,PARAMETER:http-req-body . 1747697982 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["uploadedAttachments"], "properties": {"uploadedAttachments": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2698219857_uploadedAttachments"}}}}, "176516153_pageTitle": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->fields->pageTitle . 176516153 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "1774152645_toolTip": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip . 1774152645 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"content": {"$ref": "#/definitions/**********_content"}, "headingContent": {"$ref": "#/definitions/*********_headingContent"}, "headingHint": {"$ref": "#/definitions/*********_headingHint"}, "headingLabel": {"$ref": "#/definitions/873742939_headingLabel"}, "hint": {"$ref": "#/definitions/**********_hint"}, "label": {"$ref": "#/definitions/**********_label"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/balance-breakdown,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["definedBenefitDetails", "unrestricted", "restricted", "preserved", "accumulation", "definedBenefit"], "properties": {"accumulation": {"type": "number", "format": "float64"}, "definedBenefit": {"type": "number", "format": "float64"}, "definedBenefitDetails": {}, "preserved": {"type": "number", "format": "float64"}, "restricted": {"type": "number", "format": "float64"}, "unrestricted": {"type": "number", "format": "float64"}}}, "**********_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body->sitecore->route->fields . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pageTitle"], "properties": {"pageTitle": {"$ref": "#/definitions/**********_pageTitle"}}}, "1804856_retirementPlanning": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/more,METHOD:GET,PARAMETER:http-rsp-body->retirementPlanning . 1804856 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["hasRICData", "retirementPlanningEligibility", "hasRSCData"], "properties": {"hasRICData": {"type": "boolean"}, "hasRSCData": {"type": "boolean"}, "retirementPlanningEligibility": {"type": "string"}}}, "**********_beneficiaries": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/benefits-nominations,METHOD:GET,PARAMETER:http-rsp-body->beneficiaries->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "percentage": {"type": "number", "format": "float64"}, "relationship": {"type": "string"}, "reversionaryDetails": {}}}, "**********_text": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields->text . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["accountDetails", "totalBalance", "crmContactId"], "properties": {"accountDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_accountDetails"}}, "crmContactId": {"type": "string"}, "totalBalance": {"type": "number", "format": "float64"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["investmentReturns", "superComparison", "estimatedBalance", "balanceOverTime", "marketingDigitalMessage", "calculatorEligibility", "superWithdrawalEligibility", "flexiPensionWithdrawalEligibility", "pensionIncome", "contributionEligibility", "dateCalculatorLastSaved", "lastSuperContributions"], "properties": {"balanceOverTime": {"$ref": "#/definitions/*********_balanceOverTime"}, "calculatorEligibility": {"type": "string"}, "contributionEligibility": {"type": "boolean"}, "dateCalculatorLastSaved": {}, "estimatedBalance": {"$ref": "#/definitions/*********_estimatedBalance"}, "flexiPensionWithdrawalEligibility": {}, "investmentReturns": {}, "lastSuperContributions": {"$ref": "#/definitions/**********_lastSuperContributions"}, "marketingDigitalMessage": {"$ref": "#/definitions/**********_marketingDigitalMessage"}, "pensionIncome": {}, "superComparison": {}, "superWithdrawalEligibility": {"type": "boolean"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["insurance", "investmentChoice", "rollover", "contactDetails", "personalDetails"], "properties": {"contactDetails": {"$ref": "#/definitions/**********_contactDetails"}, "insurance": {"$ref": "#/definitions/**********_insurance"}, "investmentChoice": {"$ref": "#/definitions/**********_investmentChoice"}, "personalDetails": {"$ref": "#/definitions/**********_personalDetails"}, "rollover": {"$ref": "#/definitions/**********_rollover"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/status,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["notificationType", "notificationDescription", "notificationHeader", "siteMaintenance"], "properties": {"notificationDescription": {}, "notificationHeader": {}, "notificationType": {"type": "string"}, "siteMaintenance": {"type": "boolean"}}}, "1850111827_validateRegistrationRequest": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/validate-registration,METHOD:POST,PARAMETER:http-req-body->validateRegistrationRequest . 1850111827 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["surname", "memberNumber", "dateOfBirth"], "properties": {"dateOfBirth": {"type": "string"}, "memberNumber": {"type": "string"}, "surname": {"type": "string"}}}, "1869272553_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-event-processor/notifications/trigger,METHOD:POST,PARAMETER:http-rsp-body->content . 1869272553 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["headers"], "properties": {"headers": {"description": " ", "type": "array", "items": {}}}}, "**********_sitecore": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["route"], "properties": {"route": {"$ref": "#/definitions/2304701_route"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/bpay-details,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["spouseContributions", "personalContributions"], "properties": {"personalContributions": {"$ref": "#/definitions/**********_personalContributions"}, "spouseContributions": {"$ref": "#/definitions/**********_spouseContributions"}}}, "**********_pensionIncome": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/pension-payments,METHOD:GET,PARAMETER:http-rsp-body->pensionIncome . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["nextPaymentDate", "paymentAmount", "paymentFrequency"], "properties": {"nextPaymentDate": {"type": "string"}, "paymentAmount": {"type": "number", "format": "float64"}, "paymentFrequency": {"type": "string"}}}, "*********_text": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"content": {"$ref": "#/definitions/3365371663_content"}, "hint": {"$ref": "#/definitions/*********_hint"}, "label": {"$ref": "#/definitions/*********_label"}}}, "1927603818_body": {"description": "Definition for RESPONSE CODE:400,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/anonymous-search,METHOD:POST,PARAMETER:http-rsp-body->>* . 1927603818 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"Code": {"type": "string"}, "Message": {"type": "string"}}}, "**********_jss-app-content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"componentName": {"type": "string"}, "fields": {"$ref": "#/definitions/**********_fields"}}}, "**********_investmentComponents": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->balanceOverTime->balanceSnapshots->>*->investmentComponents->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["balanceAmount", "componentName"], "properties": {"balanceAmount": {"type": "number", "format": "float64"}, "componentName": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["transactionsID", "documentID"], "properties": {"documentID": {"type": "string"}, "transactionsID": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/communication-preferences,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["statements"], "properties": {"statements": {"type": "string"}}}, "*********_balanceOverTime": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->balanceOverTime . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["success", "balanceSnapshots"], "properties": {"balanceSnapshots": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_balanceSnapshots"}}, "success": {"type": "boolean"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["prospectID"], "properties": {"prospectID": {"type": "string"}}}, "**********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->content . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_memberAccountAlias": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/members/alias,METHOD:GET,PARAMETER:http-rsp-body->memberAlias->>*->memberAccountAlias->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["accountNumber", "accountStatus", "uniqueAccountId", "externalId", "productType", "accountStartDate", "fund"], "properties": {"accountNumber": {"type": "string"}, "accountStartDate": {"type": "string"}, "accountStatus": {"type": "string"}, "externalId": {"type": "string"}, "fund": {"type": "string"}, "productType": {"type": "string"}, "uniqueAccountId": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/investment-allocations,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["estimatedTotalInvestmentReturns", "estimatedBalance", "allocations"], "properties": {"allocations": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_allocations"}}, "estimatedBalance": {"type": "number", "format": "float64"}, "estimatedTotalInvestmentReturns": {"type": "number", "format": "float64"}}}, "**********_history": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/balance/history,METHOD:GET,PARAMETER:http-rsp-body->history->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["definedBenefitComponent", "balance", "effectiveDate", "isLatestBalance", "accumulationComponent"], "properties": {"accumulationComponent": {}, "balance": {"type": "number", "format": "float64"}, "definedBenefitComponent": {}, "effectiveDate": {"type": "string"}, "isLatestBalance": {"type": "boolean"}}}, "**********_deathBenefit": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/insurance-cover,METHOD:GET,PARAMETER:http-rsp-body->accumulation->deathBenefit . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["amount", "units", "totalCover"], "properties": {"amount": {"type": "number", "format": "float64"}, "totalCover": {"type": "number", "format": "float64"}, "units": {"type": "number", "format": "float64"}}}, "**********_personalDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->personalDetails . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"dob": {"type": "string"}, "givenNames": {"type": "string"}, "sexAtBirth": {"type": "string"}, "surname": {"type": "string"}, "title": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["bookingId"], "properties": {"bookingId": {"type": "string"}}}, "21156085_contactDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->contactDetails . 21156085 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["mobileNumber", "emailAddress", "address"], "properties": {"address": {"$ref": "#/definitions/**********_address"}, "emailAddress": {"type": "string"}, "mobileNumber": {"type": "string"}}}, "2162206317_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/ev/*/confirm-verification,METHOD:POST,PARAMETER:http-req-body . 2162206317 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object"}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/agents/chat-availability-v2,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["agentStatus", "emergencySchedule", "chatSchedule"], "properties": {"agentStatus": {"type": "boolean"}, "chatSchedule": {"type": "boolean"}, "emergencySchedule": {"type": "boolean"}}}, "**********_contributionHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->contributionHistory->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["periodStartDate", "fileReference", "status", "accum1MemberPreTax", "totalAmount", "dBDAccum2Employer", "accum1MemberPostTax", "accum1EmployerCasual", "unallocatedAmount", "errorDetails", "dBDAccum2MemberPostTax", "dBDAccum2MemberPreTax", "periodEndDate", "accum1SGAward", "refundedAmount"], "properties": {"accum1EmployerCasual": {"type": "number", "format": "float64"}, "accum1MemberPostTax": {"type": "number", "format": "float64"}, "accum1MemberPreTax": {"type": "number", "format": "float64"}, "accum1SGAward": {}, "dBDAccum2Employer": {"type": "number", "format": "float64"}, "dBDAccum2MemberPostTax": {"type": "number", "format": "float64"}, "dBDAccum2MemberPreTax": {"type": "number", "format": "float64"}, "errorDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_errorDetails"}}, "fileReference": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodStartDate": {"type": "string"}, "refundedAmount": {"type": "number", "format": "float64"}, "status": {"type": "string"}, "totalAmount": {"type": "number", "format": "float64"}, "unallocatedAmount": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:401,CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"message": {"type": "string"}, "statusCode": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/balance/history,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["history"], "properties": {"history": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_history"}}}}, "**********_memberAlias": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/members/alias,METHOD:GET,PARAMETER:http-rsp-body->memberAlias->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberAccountAlias", "userName", "memberNumber", "externalId"], "properties": {"externalId": {"type": "string"}, "memberAccountAlias": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_memberAccountAlias"}}, "memberNumber": {"type": "string"}, "userName": {"type": "string"}}}, "**********_jss-app-announcements": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"componentName": {"type": "string"}, "fields": {"$ref": "#/definitions/565347764_fields"}}}, "2304701_route": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route . 2304701 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["placeholders", "fields"], "properties": {"fields": {"$ref": "#/definitions/3460171952_fields"}, "placeholders": {"$ref": "#/definitions/**********_placeholders"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transactions-activity,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["transactionActivities"], "properties": {"transactionActivities": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_transactionActivities"}}}}, "**********_lineItems": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->deathBenefit->lineItems->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["text", "effectiveDate", "amount"], "properties": {"amount": {"type": "number", "format": "float64"}, "effectiveDate": {}, "text": {"type": "string"}}}, "**********_transactionDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transaction-summary,METHOD:GET,PARAMETER:http-rsp-body->transactionSummaries->>*->transactionDetails->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["amount", "transactionsType"], "properties": {"amount": {"type": "number", "format": "float64"}, "transactionsType": {"type": "string"}}}, "**********_OpenVariancesStatistics": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/variance/dashboard,METHOD:GET,PARAMETER:http-rsp-body->OpenVariancesStatistics->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["numberOfOpenVariance", "dateCreated"], "properties": {"dateCreated": {"type": "string"}, "numberOfOpenVariance": {"type": "number", "format": "float64"}}}, "2361654654_premiums": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails->premiums . 2361654654 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"death": {}, "ip": {"type": "number", "format": "float64"}, "tpd": {}}}, "**********_fytdPerformance": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->performanceData->fytdPerformance->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value", "returnDate"], "properties": {"returnDate": {"type": "string"}, "value": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/searchmemberaccount,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["keyword", "taxfilenumber", "dateofbirth", "surname", "<PERSON><PERSON><PERSON>"], "properties": {"dateofbirth": {"type": "string"}, "givenName": {"type": "string"}, "keyword": {"type": "string"}, "surname": {"type": "string"}, "taxfilenumber": {"type": "string"}}}, "**********___Updated": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->__Updated . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_insurance": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["ip", "questions", "premiums", "tpd", "death", "requested", "occupationRatingTypes"], "properties": {"death": {"$ref": "#/definitions/*********_death"}, "ip": {"$ref": "#/definitions/*********_ip"}, "occupationRatingTypes": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_occupationRatingTypes"}}, "premiums": {"$ref": "#/definitions/**********_premiums"}, "questions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_questions"}}, "requested": {"type": "boolean"}, "tpd": {"$ref": "#/definitions/**********_tpd"}}}, "2459695600_contacts": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/validate-registration,METHOD:POST,PARAMETER:http-rsp-body->contacts->>* . 2459695600 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["maskedValue", "contactType", "key", "contactValue"], "properties": {"contactType": {"type": "string"}, "contactValue": {"type": "string"}, "key": {"type": "number", "format": "float64"}, "maskedValue": {"type": "string"}}}, "**********_prospect": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->prospect . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["prospectID", "status"], "properties": {"prospectID": {"type": "string"}, "status": {"type": "string"}}}, "**********_notionalTaxedContribution": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/contributions-and-caps,METHOD:GET,PARAMETER:http-rsp-body->notionalTaxedContribution . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["ntcGrandFatheringAbility", "accumulationAmount", "notionalAmount"], "properties": {"accumulationAmount": {"type": "number", "format": "float64"}, "notionalAmount": {"type": "number", "format": "float64"}, "ntcGrandFatheringAbility": {"type": "boolean"}}}, "**********_address": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->contactDetails->address . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["state", "postcode", "suburb", "addressLine2", "addressLine1", "country"], "properties": {"addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "*********_bankDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/pension-payments,METHOD:GET,PARAMETER:http-rsp-body->bankDetails->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["accountName", "accountNumber", "percentage", "bsb", "institutionName"], "properties": {"accountName": {"type": "string"}, "accountNumber": {"type": "string"}, "bsb": {"type": "string"}, "institutionName": {"type": "string"}, "percentage": {"type": "number", "format": "float64"}}}, "*********_tenYearPerformance": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->performanceData->tenYearPerformance->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["returnDate", "value"], "properties": {"returnDate": {"type": "string"}, "value": {"type": "number", "format": "float64"}}}, "**********_lastContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/contributions-and-caps,METHOD:GET,PARAMETER:http-rsp-body->lastContributions->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["amount", "description", "date"], "properties": {"amount": {"type": "number", "format": "float64"}, "date": {"type": "string"}, "description": {"type": "string"}}}, "*********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments/08p9r000000mAebAAE,METHOD:PUT,PARAMETER:http-req-body . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object"}, "**********_insuranceQuestions": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails->insuranceQuestions->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["questionType", "response", "questionText"], "properties": {"questionText": {"type": "string"}, "questionType": {"type": "string"}, "response": {"type": "boolean"}}}, "**********_employmentHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->employmentHistory->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["payrollNumber", "terminationDate", "employer", "effectiveDate", "reason"], "properties": {"effectiveDate": {"type": "string"}, "employer": {"type": "string"}, "payrollNumber": {"type": "string"}, "reason": {"type": "string"}, "terminationDate": {}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/anonymous-search,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["postcode", "mobileNumber", "emailAddress", "surname"], "properties": {"GivenNames": {"type": "string"}, "dateOfBirth": {"type": "string"}, "emailAddress": {"type": "string"}, "givenNames": {"type": "string"}, "mobileNumber": {"type": "string"}, "postcode": {"type": "string"}, "surname": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/members/accounts,METHOD:GET,PARAMETER:http-rsp-body->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberNumber", "externalId", "accountStartDate", "productType", "userName", "accountNumber", "accountStatus", "memberType", "usmi"], "properties": {"accountNumber": {"type": "string"}, "accountStartDate": {"type": "string"}, "accountStatus": {"type": "string"}, "externalId": {"type": "string"}, "memberNumber": {"type": "string"}, "memberType": {"type": "string"}, "productType": {"type": "string"}, "userName": {"type": "string"}, "usmi": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/searchmemberaccount,METHOD:POST,PARAMETER:http-rsp-body->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["familyname", "givenname", "terminationdate", "contributionratepost", "contributionratepre", "accounttypesummaryname", "accounttypecode", "membernumber"], "properties": {"accounttypecode": {"type": "string"}, "accounttypesummaryname": {"type": "string"}, "contributionratepost": {"type": "number", "format": "float64"}, "contributionratepre": {"type": "number", "format": "float64"}, "familyname": {"type": "string"}, "givenname": {"type": "string"}, "membernumber": {"type": "string"}, "terminationdate": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/status,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["warnMessageHeading", "errorMessageText", "errorMessageHeading", "appVersion", "warnMessageText", "contentAvailable", "available", "recommendedOsVersion", "disabledFeatures", "minAppVersion"], "properties": {"appVersion": {"$ref": "#/definitions/**********_appVersion"}, "available": {"type": "boolean"}, "contentAvailable": {"type": "boolean"}, "disabledFeatures": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "errorMessageHeading": {"type": "string"}, "errorMessageText": {"type": "string"}, "minAppVersion": {"type": "string"}, "recommendedOsVersion": {"type": "string"}, "warnMessageHeading": {"type": "string"}, "warnMessageText": {"type": "string"}}}, "**********_varianceHistoryDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->varianceHistory->varianceHistoryDetails->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["allTimeEmployerVariance", "expectedMemberPostTax", "expectedEmployer", "actualEmployer", "actualMemberPreTax", "periodEmployerV<PERSON>ce", "periodEndDate", "expectedMemberPreTax", "varianceDays", "membershipUpdates", "periodStartDate", "actualMemberPostTax", "allTimeMemberVariance", "periodMember<PERSON><PERSON>ce"], "properties": {"actualEmployer": {"type": "string"}, "actualMemberPostTax": {"type": "string"}, "actualMemberPreTax": {"type": "string"}, "allTimeEmployerVariance": {"type": "string"}, "allTimeMemberVariance": {"type": "string"}, "expectedEmployer": {"type": "string"}, "expectedMemberPostTax": {"type": "string"}, "expectedMemberPreTax": {"type": "string"}, "membershipUpdates": {"description": " ", "type": "array", "items": {}}, "periodEmployerVariance": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodMemberVariance": {"type": "string"}, "periodStartDate": {}, "varianceDays": {"type": "string"}}}, "2652747550_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->fields . 2652747550 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pageTitle"], "properties": {"pageTitle": {"$ref": "#/definitions/176516153_pageTitle"}}}, "**********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->content . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_residentialAddress": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->contactDetails->residentialAddress . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["autoAddress", "country", "state", "postcode", "suburb", "address2", "address1"], "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "autoAddress": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "**********_heading": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields->heading . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "2698219857_uploadedAttachments": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/process-files,METHOD:POST,PARAMETER:http-req-body->uploadedAttachments->>* . 2698219857 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["fileName", "blobUploadDate"], "properties": {"blobUploadDate": {"type": "string"}, "fileName": {"type": "string"}}}, "2723778075_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->content . 2723778075 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["prospect"], "properties": {"campaign": {"$ref": "#/definitions/3954459169_campaign"}, "contactDetails": {"$ref": "#/definitions/3370818041_contactDetails"}, "personalDetails": {"$ref": "#/definitions/**********_personalDetails"}, "prospect": {"$ref": "#/definitions/**********_prospect"}}}, "**********_occupationRatingTypes": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->occupationRatingTypes->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["icon", "examples", "description", "name", "code"], "properties": {"code": {"type": "number", "format": "float64"}, "description": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "examples": {"type": "string"}, "icon": {"type": "string"}, "name": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/utility/address/search,METHOD:POST,PARAMETER:http-rsp-body->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["fullAddress", "street", "streetNumber", "subdwelling", "locality", "state", "postcode", "country"], "properties": {"country": {}, "fullAddress": {"type": "string"}, "locality": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "street": {"type": "string"}, "streetNumber": {"type": "string"}, "subdwelling": {"type": "string"}}}, "2768198023_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/document/member-correspondence,METHOD:POST,PARAMETER:http-req-body . 2768198023 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["documentMimeType", "documentOwnerIdentifierScheme", "documentOwnerIdentifier", "documentUserCode", "documentName", "documentDescription", "documentSource", "documentIdentifier", "documentContent"], "properties": {"attachments": {}, "documentContent": {"$ref": "#/definitions/*********_documentContent"}, "documentDescription": {"type": "string"}, "documentIdentifier": {"type": "string"}, "documentMimeType": {"type": "string"}, "documentName": {"type": "string"}, "documentOwnerIdentifier": {"type": "string"}, "documentOwnerIdentifierScheme": {"type": "string"}, "documentSource": {"type": "string"}, "documentUserCode": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/benefits-nominations,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["nominationType", "expiryDate", "beneficiaries"], "properties": {"beneficiaries": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_beneficiaries"}}, "expiryDate": {}, "nominationType": {}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-event-processor/notifications/trigger,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["content", "version", "isSuccessStatusCode", "requestMessage", "trailingHeaders", "headers", "reason<PERSON><PERSON><PERSON>", "statusCode"], "properties": {"content": {"$ref": "#/definitions/1869272553_content"}, "headers": {"description": " ", "type": "array", "items": {}}, "isSuccessStatusCode": {"type": "boolean"}, "reasonPhrase": {"type": "string"}, "requestMessage": {}, "statusCode": {"type": "string"}, "trailingHeaders": {"description": " ", "type": "array", "items": {}}, "version": {"type": "string"}}}, "2899895377_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->label . 2899895377 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********___Updated": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields->__Updated . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_serviceHistoryUpdates": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance,METHOD:PUT,PARAMETER:http-req-body->serviceHistoryUpdates->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["benefitEligibility", "reason", "endDate", "startDate", "action", "serviceFraction", "academicGeneral", "workingStatus"], "properties": {"academicGeneral": {"type": "string"}, "action": {"type": "string"}, "benefitEligibility": {"type": "string"}, "endDate": {"type": "string"}, "reason": {"type": "string"}, "serviceFraction": {"type": "number", "format": "float64"}, "startDate": {"type": "string"}, "workingStatus": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/document/insert,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["documentContent", "documentSource", "documentName", "documentUserCode", "documentOwnerIdentifierScheme", "documentOwnerIdentifier", "documentDescription", "documentIdentifier", "documentMimeType"], "properties": {"attachments": {}, "documentContent": {"$ref": "#/definitions/3184910352_documentContent"}, "documentDescription": {"type": "string"}, "documentIdentifier": {"type": "string"}, "documentMimeType": {"type": "string"}, "documentName": {"type": "string"}, "documentOwnerIdentifier": {"type": "string"}, "documentOwnerIdentifierScheme": {"type": "string"}, "documentSource": {"type": "string"}, "documentUserCode": {"type": "string"}}}, "**********_jss-app-content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["componentName", "fields"], "properties": {"componentName": {"type": "string"}, "fields": {"$ref": "#/definitions/3603953744_fields"}}}, "**********_lineItems": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->totalAndPermanentDisablementBenefit->lineItems->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["effectiveDate", "amount", "text"], "properties": {"amount": {"type": "number", "format": "float64"}, "effectiveDate": {}, "text": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance,METHOD:PUT,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["referenceIds"], "properties": {"referenceIds": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/variance/summary,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["varianceActionHistory", "outstandingVariances"], "properties": {"outstandingVariances": {"description": " ", "type": "array", "items": {}}, "varianceActionHistory": {"description": " ", "type": "array", "items": {}}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments/*,METHOD:PUT,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"previousConsultantId": {"type": "string"}}}, "3050441699_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/vercellogprocessor/vercel/VercelWebhookPayload,METHOD:POST,PARAMETER:http-req-body->>* . 3050441699 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["host", "source", "deploymentId", "timestamp", "projectId", "id"], "properties": {"branch": {"type": "string"}, "buildId": {"type": "string"}, "deploymentId": {"type": "string"}, "destination": {"type": "string"}, "edgeType": {"type": "string"}, "entrypoint": {"type": "string"}, "environment": {"type": "string"}, "executionRegion": {"type": "string"}, "host": {"type": "string"}, "id": {"type": "string"}, "ja4Digest": {"type": "string"}, "level": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}, "projectId": {"type": "string"}, "projectName": {"type": "string"}, "proxy": {"$ref": "#/definitions/3055810503_proxy"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "statusCode": {"type": "number", "format": "float64"}, "timestamp": {"type": "number", "format": "float64"}, "type": {"type": "string"}}}, "3055810503_proxy": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/vercellogprocessor/vercel/VercelWebhookPayload,METHOD:POST,PARAMETER:http-req-body->>*->proxy . 3055810503 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"cacheId": {"type": "string"}, "clientIp": {"type": "string"}, "errorCode": {"type": "string"}, "host": {"type": "string"}, "lambdaRegion": {"type": "string"}, "method": {"type": "string"}, "path": {"type": "string"}, "pathType": {"type": "string"}, "pathTypeVariant": {"type": "string"}, "referer": {"type": "string"}, "region": {"type": "string"}, "scheme": {"type": "string"}, "statusCode": {"type": "number", "format": "float64"}, "timestamp": {"type": "number", "format": "float64"}, "userAgent": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "vercelCache": {"type": "string"}, "vercelId": {"type": "string"}, "wafAction": {"type": "string"}, "wafRule": {"type": "string"}, "wafRuleId": {"type": "string"}}}, "**********_memberDetails": {"description": "Definition for RESPONSE CODE:202,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-rsp-body->memberDetails . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"isDefinedBenefit": {"type": "boolean"}, "memberElectContributionRatePostTax": {"type": "string"}, "memberElectContributionRatePreTax": {"type": "string"}, "memberNumber": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/transition-cover,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["preTransition", "description", "isEligible", "postTransition"], "properties": {"description": {}, "isEligible": {"type": "boolean"}, "postTransition": {}, "preTransition": {}}}, "**********_appVersion": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/status,METHOD:GET,PARAMETER:http-rsp-body->appVersion . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["unsupportedVersions", "minAppVersion", "recommendedAppVersion"], "properties": {"minAppVersion": {"type": "string"}, "recommendedAppVersion": {"type": "string"}, "unsupportedVersions": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}}}, "**********_subHeading": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["label", "hint", "content"], "properties": {"content": {"$ref": "#/definitions/**********_content"}, "hint": {"$ref": "#/definitions/3212234624_hint"}, "label": {"$ref": "#/definitions/3149541591_label"}}}, "**********_totalAndPermanentDisablementBenefit": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->totalAndPermanentDisablementBenefit . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["isCoverPresent", "lineItems", "totalBenefitAmount", "totalBenefitText"], "properties": {"isCoverPresent": {"type": "boolean"}, "lineItems": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_lineItems"}}, "totalBenefitAmount": {"type": "number", "format": "float64"}, "totalBenefitText": {"type": "string"}}}, "**********_residentialAddress": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->contactDetails->residentialAddress . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "autoAddress": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "3138481986_topicCodes": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/interactions,METHOD:POST,PARAMETER:http-req-body->topicCodes->>* . 3138481986 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "3149541591_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->label . 3149541591 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "3184910352_documentContent": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/document/insert,METHOD:POST,PARAMETER:http-req-body->documentContent . 3184910352 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["document", "documentContentSource"], "properties": {"document": {"type": "string"}, "documentContentSource": {"type": "string"}}}, "3185483557_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/variance/dashboard,METHOD:GET,PARAMETER:http-rsp-body . 3185483557 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["OpenVariancesStatistics", "OpenVarianceDetails", "OpenVariancesSummary"], "properties": {"OpenVarianceDetails": {"description": " ", "type": "array", "items": {}}, "OpenVariancesStatistics": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_OpenVariancesStatistics"}}, "OpenVariancesSummary": {"$ref": "#/definitions/3225540032_OpenVariancesSummary"}}}, "3193457757_announcements": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>* . 3193457757 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"fields": {"$ref": "#/definitions/**********_fields"}, "id": {"type": "string"}}}, "3212234624_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->hint . 3212234624 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "3225540032_OpenVariancesSummary": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/variance/dashboard,METHOD:GET,PARAMETER:http-rsp-body->OpenVariancesSummary . 3225540032 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["totalVariances", "varianceSummaryDetails"], "properties": {"totalVariances": {"type": "number", "format": "float64"}, "varianceSummaryDetails": {"description": " ", "type": "array", "items": {}}}}, "********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/*/contact/phones,METHOD:PUT,PARAMETER:http-req-body . ******** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["phones"], "properties": {"phones": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_phones"}}}}, "**********_premiums": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->premiums . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["death", "deathAndTpd", "ip", "tpd"], "properties": {"death": {"type": "number", "format": "float64"}, "deathAndTpd": {"type": "number", "format": "float64"}, "ip": {"type": "number", "format": "float64"}, "tpd": {"type": "number", "format": "float64"}}}, "**********_placeholders": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["jss-app-content", "jss-app-announcements"], "properties": {"jss-app-announcements": {"description": " ", "type": "array", "items": {}}, "jss-app-content": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_jss-app-content"}}}}, "**********_body": {"description": "Definition for RESPONSE CODE:202,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberDetails", "referenceNumber"], "properties": {"memberDetails": {}, "referenceNumber": {"type": "string"}}}, "**********_placeholders": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body->sitecore->route->placeholders . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["jss-app-content", "jss-app-announcements"], "properties": {"jss-app-announcements": {"description": " ", "type": "array", "items": {}}, "jss-app-content": {"description": " ", "type": "array", "items": {}}}}, "**********_varianceHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->varianceHistory . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["varianceHistoryDetails", "expectedContributions"], "properties": {"expectedContributions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_expectedContributions"}}, "varianceHistoryDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_varianceHistoryDetails"}}}}, "**********_incomeProtectionCover": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->incomeProtectionCover . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["totalBenefitAmount", "totalBenefitText", "isCoverPresent", "benefitPeriod", "<PERSON><PERSON><PERSON><PERSON>", "coverText"], "properties": {"benefitPeriod": {"type": "string"}, "coverText": {"type": "string"}, "isCoverPresent": {"type": "boolean"}, "totalBenefitAmount": {"type": "number", "format": "float64"}, "totalBenefitText": {"type": "string"}, "waitingPeriod": {"type": "string"}}}, "**********_pageTitle": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body->sitecore->route->fields->pageTitle . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_tpd": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->tpd . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["coverAmount", "units", "eligibility"], "properties": {"coverAmount": {"type": "number", "format": "float64"}, "eligibility": {"type": "boolean"}, "units": {"type": "number", "format": "float64"}}}, "**********_pensionIncome": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->pensionIncome . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["nextPaymentDate", "paymentAmount", "paymentFrequency", "annualGrossIncome", "success"], "properties": {"annualGrossIncome": {"type": "number", "format": "float64"}, "nextPaymentDate": {"type": "string"}, "paymentAmount": {"type": "number", "format": "float64"}, "paymentFrequency": {"type": "string"}, "success": {"type": "boolean"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["mobileNumber", "username", "dateOfBirth", "surname", "firstName", "memberNumber"], "properties": {"dateOfBirth": {"type": "string"}, "firstName": {"type": "string"}, "memberNumber": {"type": "string"}, "mobileNumber": {"type": "string"}, "surname": {"type": "string"}, "username": {"type": "string"}}}, "3365371663_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->content . 3365371663 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "3370818041_contactDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->contactDetails . 3370818041 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"email": {"type": "string"}, "mobileNumber": {"type": "string"}, "postalAddress": {"$ref": "#/definitions/**********_postalAddress"}, "residentialAddress": {"$ref": "#/definitions/**********_residentialAddress"}, "residentialSameAsPostal": {"type": "boolean"}}}, "3371154769_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->hint . 3371154769 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["sitecore"], "properties": {"sitecore": {"$ref": "#/definitions/**********_sitecore"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/insurance-cover,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["insuranceEffectiveDate", "tpdCoverEligibilityCode", "occupationRating", "incomeProtectionEligibilityCode", "canUnderwrite", "canCancelInsurance", "deathCoverEligibilityCode", "hasExternalInsurance", "definedBenefits", "accumulation", "isEligibleToRetain", "accountBalanceEffectiveDate"], "properties": {"accountBalanceEffectiveDate": {"type": "string"}, "accumulation": {"$ref": "#/definitions/*********_accumulation"}, "canCancelInsurance": {"type": "boolean"}, "canUnderwrite": {"type": "boolean"}, "deathCoverEligibilityCode": {"type": "string"}, "definedBenefits": {}, "hasExternalInsurance": {"type": "boolean"}, "incomeProtectionEligibilityCode": {}, "insuranceEffectiveDate": {"type": "string"}, "isEligibleToRetain": {"type": "boolean"}, "occupationRating": {"type": "string"}, "tpdCoverEligibilityCode": {}}}, "**********_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->label . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "3460171952_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->fields . 3460171952 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pageTitle"], "properties": {"pageTitle": {"$ref": "#/definitions/1389509455_pageTitle"}}}, "**********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->hint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_marketingDigitalMessage": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->marketingDigitalMessage . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["titleText", "mobileImage", "desktopImage", "linkText", "linkUrl", "campaign", "marketingMessageAvailable"], "properties": {"campaign": {"type": "string"}, "desktopImage": {"type": "string"}, "linkText": {"type": "string"}, "linkUrl": {"type": "string"}, "marketingMessageAvailable": {"type": "boolean"}, "mobileImage": {"type": "string"}, "titleText": {"type": "string"}}}, "**********_expectedContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->varianceHistory->expectedContributions->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberIncVariancePreTax", "PayPeriodEmployer", "payPeriodMemberPostTax", "payPeriodMemberPreTax", "periodEndDate", "membershipUpdates", "employerIncVariance", "memberIncVariancePostTax"], "properties": {"PayPeriodEmployer": {"type": "string"}, "employerIncVariance": {}, "memberIncVariancePostTax": {"type": "string"}, "memberIncVariancePreTax": {"type": "string"}, "membershipUpdates": {}, "payPeriodMemberPostTax": {"type": "string"}, "payPeriodMemberPreTax": {"type": "string"}, "periodEndDate": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/ev/*/verification-details,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["<PERSON><PERSON><PERSON>", "dob", "surname", "transactionType", "referenceId", "verificationStatus", "addressDetails", "allDataSources", "successfulDataSources", "ruleset"], "properties": {"addressDetails": {}, "allDataSources": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "dob": {}, "givenName": {}, "referenceId": {"type": "string"}, "ruleset": {"type": "string"}, "successfulDataSources": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "surname": {}, "transactionType": {"type": "string"}, "verificationStatus": {"$ref": "#/definitions/3626405400_verificationStatus"}}}, "**********_accountDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts,METHOD:GET,PARAMETER:http-rsp-body->accountDetails->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["balanceEffectiveDate", "balance", "accountType", "memberNumber", "transactionsMade", "uniqueAccountId", "accountAlias"], "properties": {"accountAlias": {"type": "string"}, "accountType": {"type": "string"}, "balance": {"type": "number", "format": "float64"}, "balanceEffectiveDate": {"type": "string"}, "memberNumber": {"type": "string"}, "transactionsMade": {"type": "boolean"}, "uniqueAccountId": {"type": "string"}}}, "**********_incomeProtectionBenefit": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/insurance-cover,METHOD:GET,PARAMETER:http-rsp-body->accumulation->incomeProtectionBenefit . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["benefitPeriod", "<PERSON><PERSON><PERSON><PERSON>", "totalCover", "amount", "units", "canOptOutOfAutoUpdates"], "properties": {"amount": {"type": "number", "format": "float64"}, "benefitPeriod": {"type": "string"}, "canOptOutOfAutoUpdates": {"type": "boolean"}, "totalCover": {}, "units": {"type": "number", "format": "float64"}, "waitingPeriod": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/validate-registration,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["dateOfBirth", "evRequired", "registered", "isDuplicateSftMember", "isInternationalAddress", "memberNumber", "<PERSON><PERSON><PERSON><PERSON>", "surname", "firstName", "contacts", "hasEligibleAccount", "found"], "properties": {"contacts": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2459695600_contacts"}}, "dateOfBirth": {"type": "string"}, "evRequired": {"type": "boolean"}, "firstName": {"type": "string"}, "found": {"type": "boolean"}, "hasAddress": {"type": "boolean"}, "hasEligibleAccount": {"type": "boolean"}, "isDuplicateSftMember": {"type": "boolean"}, "isInternationalAddress": {"type": "boolean"}, "memberNumber": {"type": "string"}, "registered": {"type": "boolean"}, "surname": {"type": "string"}}}, "**********_tpdBenefit": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/insurance-cover,METHOD:GET,PARAMETER:http-rsp-body->accumulation->tpdBenefit . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["totalCover", "amount", "units"], "properties": {"amount": {"type": "number", "format": "float64"}, "totalCover": {"type": "number", "format": "float64"}, "units": {"type": "number", "format": "float64"}}}, "*********_documentContent": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/document/member-correspondence,METHOD:POST,PARAMETER:http-req-body->documentContent . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["document", "documentContentSource"], "properties": {"document": {"type": "string"}, "documentContentSource": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/agent,METHOD:POST,PARAMETER:http-req-body->>* . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["attributeName", "attributeValue"], "properties": {"attributeName": {"type": "string"}, "attributeValue": {"type": "string"}}}, "*********_transactionSummaries": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transaction-summary,METHOD:GET,PARAMETER:http-rsp-body->transactionSummaries->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["totalAmount", "transactionDetails", "financialYear", "financialYearDescription"], "properties": {"financialYear": {"type": "string"}, "financialYearDescription": {"type": "string"}, "totalAmount": {"type": "number", "format": "float64"}, "transactionDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_transactionDetails"}}}}, "**********_headingHint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingHint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "3603953744_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields . 3603953744 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["heading", "__Updated", "toolTip", "text", "subHeading"], "properties": {"__Updated": {"$ref": "#/definitions/**********___Updated"}, "heading": {"$ref": "#/definitions/**********_heading"}, "subHeading": {"$ref": "#/definitions/**********_subHeading"}, "text": {"$ref": "#/definitions/568668110_text"}, "toolTip": {"$ref": "#/definitions/4269198184_toolTip"}}}, "3616048921_tpd": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails->tpd . 3616048921 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"coverAmount": {}, "units": {}}}, "3625279484_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/search,METHOD:POST,PARAMETER:http-rsp-body . 3625279484 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberNumber", "isDefinedBenefit", "memberElectContributionRatePostTax", "memberElectContributionRatePreTax"], "properties": {"isDefinedBenefit": {"type": "boolean"}, "memberElectContributionRatePostTax": {"type": "number", "format": "float64"}, "memberElectContributionRatePreTax": {"type": "number", "format": "float64"}, "memberNumber": {"type": "string"}}}, "3626405400_verificationStatus": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/ev/*/verification-details,METHOD:GET,PARAMETER:http-rsp-body->verificationStatus . 3626405400 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["dateTime", "status"], "properties": {"dateTime": {"type": "string"}, "status": {"type": "string"}}}, "**********_heading": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["label", "hint", "content"], "properties": {"content": {"$ref": "#/definitions/2723778075_content"}, "hint": {"$ref": "#/definitions/3371154769_hint"}, "label": {"$ref": "#/definitions/**********_label"}}}, "**********_postalAddress": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->contactDetails->postalAddress . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["country", "state", "postcode", "suburb", "address2", "address1", "autoAddress"], "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "autoAddress": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "**********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->hint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_transactionActivities": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transactions-activity,METHOD:GET,PARAMETER:http-rsp-body->transactionActivities->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["transactions", "transactionYear", "transactionMonthName"], "properties": {"transactionMonthName": {"type": "string"}, "transactionYear": {"type": "string"}, "transactions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_transactions"}}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/contributions-and-caps,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["notionalTaxedContribution", "noticeOfIntentEligibility", "isCrystallised", "afterTaxContributions", "lastContributions", "beforeTaxContributions", "contributionsMade", "afterTaxContributionCap", "beforeTaxContributionCap", "contributionEligibility"], "properties": {"afterTaxContributionCap": {"type": "number", "format": "float64"}, "afterTaxContributions": {"type": "number", "format": "float64"}, "beforeTaxContributionCap": {"type": "number", "format": "float64"}, "beforeTaxContributions": {"type": "number", "format": "float64"}, "contributionEligibility": {"type": "boolean"}, "contributionsMade": {"type": "boolean"}, "isCrystallised": {"type": "boolean"}, "lastContributions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_lastContributions"}}, "noticeOfIntentEligibility": {"type": "boolean"}, "notionalTaxedContribution": {"$ref": "#/definitions/**********_notionalTaxedContribution"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/utility/address/search,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["address"], "properties": {"address": {"type": "string"}}}, "**********_nominatedBeneficiaries": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/beneficiaries,METHOD:GET,PARAMETER:http-rsp-body->nominatedBeneficiaries . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["isNominationExpired", "nominationExpiryDate", "nominationType", "beneficiaries"], "properties": {"beneficiaries": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_beneficiaries"}}, "isNominationExpired": {"type": "boolean"}, "nominationExpiryDate": {}, "nominationType": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/investment/details,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["data"], "properties": {"data": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/********_data"}}}}, "**********_threeYearPerformance": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->performanceData->threeYearPerformance->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["returnDate", "value"], "properties": {"returnDate": {"type": "string"}, "value": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/insurance/premium,METHOD:GET,PARAMETER:http-rsp-body->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["premiumAmount", "coverType"], "properties": {"coverType": {"type": "string"}, "premiumAmount": {"type": "number", "format": "float64"}}}, "3740040824_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->label . 3740040824 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "380560423_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/utility/titles,METHOD:GET,PARAMETER:http-rsp-body->>* . 380560423 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["gender", "titleCode", "<PERSON><PERSON><PERSON>", "sortOrder", "sexAtBirth"], "properties": {"gender": {"type": "string"}, "sexAtBirth": {"type": "string"}, "sortOrder": {"type": "number", "format": "float64"}, "titleCode": {"type": "string"}, "titleName": {"type": "string"}}}, "3813960096_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body . 3813960096 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["insuranceDetails", "productType", "rollovers", "contactDetails", "personalDetails"], "properties": {"contactDetails": {"$ref": "#/definitions/21156085_contactDetails"}, "insuranceDetails": {"$ref": "#/definitions/*********_insuranceDetails"}, "personalDetails": {"$ref": "#/definitions/1598719277_personalDetails"}, "productType": {"type": "string"}, "rollovers": {"description": " ", "type": "array", "items": {}}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/create-status,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["isMOLRegistered", "completedDateTime", "requestedDateTime", "accountNumber", "fund", "username", "requestStatus", "uniqueAccountId", "memberStatus", "memberNumber", "unisuperIdentifier"], "properties": {"accountNumber": {}, "completedDateTime": {}, "fund": {}, "isMOLRegistered": {}, "memberNumber": {}, "memberStatus": {}, "requestStatus": {"type": "string"}, "requestedDateTime": {}, "uniqueAccountId": {}, "unisuperIdentifier": {}, "username": {}}}, "**********_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"__Updated": {"$ref": "#/definitions/*********___Updated"}, "heading": {"$ref": "#/definitions/**********_heading"}, "subHeading": {"$ref": "#/definitions/**********_subHeading"}, "text": {"$ref": "#/definitions/*********_text"}, "toolTip": {"$ref": "#/definitions/1774152645_toolTip"}}}, "**********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->hint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_superContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->lastSuperContributions->superContributions->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["date", "type", "amount", "description"], "properties": {"amount": {"type": "number", "format": "float64"}, "date": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}}}, "**********_heading": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"content": {"$ref": "#/definitions/**********_content"}, "hint": {"$ref": "#/definitions/**********_hint"}, "label": {"$ref": "#/definitions/3740040824_label"}}}, "3867269308_dismissible": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields->dismissible . 3867269308 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "boolean"}}}, "**********_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->heading->label . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_accounts": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts,METHOD:GET,PARAMETER:http-rsp-body->members->>*->accounts->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["accountAliasId", "accountType", "uniqueAccountId", "balanceEffectiveDate", "balance"], "properties": {"accountAliasId": {"type": "string"}, "accountType": {"type": "string"}, "balance": {"type": "number", "format": "float64"}, "balanceEffectiveDate": {"type": "string"}, "uniqueAccountId": {"type": "string"}}}, "*********___Updated": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->__Updated . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_salaryHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->salaryHistory->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pointofTimeSalary", "effectiveDate", "oTESalary"], "properties": {"effectiveDate": {"type": "string"}, "oTESalary": {"type": "number", "format": "float64"}, "pointofTimeSalary": {"type": "number", "format": "float64"}}}, "**********_beneficiaries": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/beneficiaries,METHOD:GET,PARAMETER:http-rsp-body->nominatedBeneficiaries->beneficiaries->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["name", "relationship", "percentage"], "properties": {"name": {"type": "string"}, "percentage": {"type": "number", "format": "float64"}, "relationship": {"type": "string"}}}, "**********_sitecore": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["route"], "properties": {"route": {"$ref": "#/definitions/497591552_route"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/*/contact/addresses,METHOD:PUT,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["addresses"], "properties": {"addresses": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/1405824334_addresses"}}}}, "3954459169_campaign": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->campaign . 3954459169 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object"}, "**********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->hint . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "**********_options": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/investment/details,METHOD:GET,PARAMETER:http-rsp-body->data->>*->current->options->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["assetClass", "code", "allocation", "amount", "name"], "properties": {"allocation": {"type": "number", "format": "float64"}, "amount": {"type": "number", "format": "float64"}, "assetClass": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/details,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["refundAccountName", "isAccum2VarianceDisabled", "employerAttributes", "refundBSB", "isWehiEmployer", "employerName", "employerId", "isProtectedEmployer", "employerContactNumber", "employerAbn", "refundInstitutionName", "refundAccount"], "properties": {"employerAbn": {"type": "string"}, "employerAttributes": {"description": " ", "type": "array", "items": {}}, "employerContactNumber": {"type": "string"}, "employerId": {"type": "string"}, "employerName": {"type": "string"}, "isAccum2VarianceDisabled": {"type": "boolean"}, "isProtectedEmployer": {"type": "boolean"}, "isWehiEmployer": {"type": "boolean"}, "refundAccount": {"type": "string"}, "refundAccountName": {"type": "string"}, "refundBSB": {"type": "string"}, "refundInstitutionName": {"type": "string"}}}, "**********_spouseContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/bpay-details,METHOD:GET,PARAMETER:http-rsp-body->spouseContributions . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["customerReferenceNumber", "billerCode"], "properties": {"billerCode": {"type": "string"}, "customerReferenceNumber": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/more,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["retirementPlanning"], "properties": {"retirementPlanning": {"$ref": "#/definitions/1804856_retirementPlanning"}}}, "**********_contactDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/personal-details,METHOD:GET,PARAMETER:http-rsp-body->contactDetails . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["mobileNumber", "homePhoneNumber", "workEmail", "personalEmail", "workPhoneNumber"], "properties": {"homePhoneNumber": {}, "mobileNumber": {"type": "string"}, "personalEmail": {"type": "string"}, "workEmail": {}, "workPhoneNumber": {}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/memberinteraction/bi-datafeed/trigger-functions,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["source"], "properties": {"source": {"type": "string"}}}, "**********_placeholders": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["jss-app-content", "jss-app-announcements"], "properties": {"jss-app-announcements": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_jss-app-announcements"}}, "jss-app-content": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_jss-app-content"}}}}, "**********_lastSuperContributions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->lastSuperContributions . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["superContributions", "success"], "properties": {"success": {"type": "boolean"}, "superContributions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_superContributions"}}}}, "**********_current": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/investment/details,METHOD:GET,PARAMETER:http-rsp-body->data->>*->current . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pendingEffectiveDate", "lastUpdated", "title", "options", "total"], "properties": {"lastUpdated": {"type": "string"}, "options": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_options"}}, "pendingEffectiveDate": {"type": "string"}, "title": {"type": "string"}, "total": {"type": "number", "format": "float64"}}}, "**********_serviceHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->serviceHistory->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["academicGeneral", "serviceFraction", "benefitEligibility", "reason", "endDate", "startDate", "workingStatus"], "properties": {"academicGeneral": {"type": "string"}, "benefitEligibility": {"type": "string"}, "endDate": {}, "reason": {"type": "string"}, "serviceFraction": {"type": "number", "format": "float64"}, "startDate": {"type": "string"}, "workingStatus": {"type": "string"}}}, "**********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->content . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/investment/returns,METHOD:GET,PARAMETER:http-rsp-body->>* . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["objectiveHurdle", "objectivePeriod", "optionObjectiveReturn", "optionName", "optionId", "effectiveDate"], "properties": {"effectiveDate": {"type": "string"}, "objectiveHurdle": {"type": "number", "format": "float64"}, "objectivePeriod": {"type": "number", "format": "float64"}, "optionId": {"type": "number", "format": "float64"}, "optionName": {"type": "string"}, "optionObjectiveReturn": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/mfa/initiate,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["transactionType", "factorType", "factorId"], "properties": {"factorId": {"type": "string"}, "factorType": {"type": "string"}, "transactionType": {"type": "string"}}}, "*********_route": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body->sitecore->route . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["placeholders", "fields"], "properties": {"fields": {"$ref": "#/definitions/**********_fields"}, "placeholders": {"$ref": "#/definitions/**********_placeholders"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["userName", "usmi", "members"], "properties": {"members": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_members"}}, "userName": {"type": "string"}, "usmi": {"type": "string"}}}, "**********_headingContent": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingContent . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/pension-payments,METHOD:GET,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["pensionIncome", "incomePayment", "flexiPensionWithdrawalEligibility", "centrelinkScheduleEligibility", "bankDetails"], "properties": {"bankDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_bankDetails"}}, "centrelinkScheduleEligibility": {"type": "boolean"}, "flexiPensionWithdrawalEligibility": {"type": "boolean"}, "incomePayment": {"$ref": "#/definitions/**********_incomePayment"}, "pensionIncome": {"$ref": "#/definitions/**********_pensionIncome"}}}, "**********_investmentChoice": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->investmentChoice . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["options", "requested"], "properties": {"options": {}, "requested": {"type": "boolean"}}}, "**********_postalAddress": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/prospect,METHOD:POST,PARAMETER:http-req-body->contactDetails->postalAddress . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"address1": {"type": "string"}, "address2": {"type": "string"}, "autoAddress": {"type": "string"}, "country": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/search,METHOD:POST,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["surname", "middleName", "<PERSON><PERSON><PERSON>", "taxFileNumber", "emailAddress", "mobileNumber", "dateOfBirth"], "properties": {"dateOfBirth": {"type": "string"}, "emailAddress": {"type": "string"}, "givenName": {"type": "string"}, "middleName": {"type": "string"}, "mobileNumber": {"type": "string"}, "surname": {"type": "string"}, "taxFileNumber": {"type": "string"}}}, "*********_estimatedBalance": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/dashboard,METHOD:GET,PARAMETER:http-rsp-body->estimatedBalance . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["tapTerm", "tapStartDate", "balanceEffectiveDate", "balanceBreakdown", "totalBalance", "success"], "properties": {"balanceBreakdown": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_balanceBreakdown"}}, "balanceEffectiveDate": {"type": "string"}, "success": {"type": "boolean"}, "tapStartDate": {"type": "string"}, "tapTerm": {"type": "number", "format": "float64"}, "totalBalance": {"type": "number", "format": "float64"}}}, "**********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["sitecore"], "properties": {"sitecore": {"$ref": "#/definitions/*********_sitecore"}}}, "**********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance,METHOD:PUT,PARAMETER:http-req-body . ********** : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["employmentHistoryUpdates", "varianceCreatedDate", "serviceHistoryUpdates", "salaryHistoryUpdates"], "properties": {"employmentHistoryUpdates": {"description": " ", "type": "array", "items": {}}, "salaryHistoryUpdates": {"description": " ", "type": "array", "items": {}}, "serviceHistoryUpdates": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_serviceHistoryUpdates"}}, "varianceCreatedDate": {"type": "string"}}}, "**********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->content . ********** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value"], "properties": {"value": {"type": "string"}}}, "4263015308_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/member-search,METHOD:POST,PARAMETER:http-req-body . 4263015308 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["keyword"], "properties": {"keyword": {"type": "string"}}}, "4269198184_toolTip": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip . 4269198184 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["headingLabel", "headingContent", "content", "headingHint", "hint", "label"], "properties": {"content": {"$ref": "#/definitions/**********_content"}, "headingContent": {"$ref": "#/definitions/**********_headingContent"}, "headingHint": {"$ref": "#/definitions/**********_headingHint"}, "headingLabel": {"$ref": "#/definitions/**********_headingLabel"}, "hint": {"$ref": "#/definitions/**********_hint"}, "label": {"$ref": "#/definitions/**********_label"}}}, "4286212836_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/files,METHOD:GET,PARAMETER:http-rsp-body->>* . 4286212836 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["uploadDate", "periodDate", "memberNumber", "fileMetadataId", "employerName", "UniqueFileName", "uploadedBy", "blobUrl", "employerNumber", "filename", "category"], "properties": {"UniqueFileName": {"type": "string"}, "blobUrl": {"type": "string"}, "category": {"type": "string"}, "employerName": {"type": "string"}, "employerNumber": {"type": "string"}, "fileMetadataId": {"type": "number", "format": "float64"}, "filename": {"type": "string"}, "memberNumber": {"type": "string"}, "periodDate": {}, "uploadDate": {"type": "string"}, "uploadedBy": {"type": "string"}}}, "*********_accumulation": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/insurance-cover,METHOD:GET,PARAMETER:http-rsp-body->accumulation . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["tpdBenefit", "deathBenefit", "accumBalance", "incomeProtectionBenefit"], "properties": {"accumBalance": {"type": "number", "format": "float64"}, "deathBenefit": {"$ref": "#/definitions/**********_deathBenefit"}, "incomeProtectionBenefit": {"$ref": "#/definitions/**********_incomeProtectionBenefit"}, "tpdBenefit": {"$ref": "#/definitions/**********_tpdBenefit"}}}, "********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/insurance/rates,METHOD:GET,PARAMETER:http-rsp-body->>* . ******** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["premiumAmount", "sumInsured", "coverType"], "properties": {"coverType": {"type": "string"}, "premiumAmount": {"type": "number", "format": "float64"}, "sumInsured": {"type": "number", "format": "float64"}}}, "470469948_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/members/alias,METHOD:GET,PARAMETER:http-rsp-body . 470469948 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["memberAlias", "externalId", "usmi"], "properties": {"externalId": {"type": "string"}, "memberAlias": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_memberAlias"}}, "usmi": {"type": "string"}}}, "497591552_route": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route . 497591552 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["placeholders", "fields"], "properties": {"fields": {"$ref": "#/definitions/2652747550_fields"}, "placeholders": {"$ref": "#/definitions/**********_placeholders"}}}, "518809641_assetAllocations": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/investment/allocations,METHOD:GET,PARAMETER:http-rsp-body->>*->assetAllocations->>* . 518809641 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["code", "allocation", "description"], "properties": {"allocation": {"type": "number", "format": "float64"}, "code": {"type": "string"}, "description": {"type": "string"}}}, "544601243_bookings": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments/reminders/send,METHOD:POST,PARAMETER:http-req-body->bookings->>* . 544601243 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["bookingId"], "properties": {"bookingId": {"type": "string"}}}, "561202187_fiveYearPerformance": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->performanceData->fiveYearPerformance->>* . 561202187 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["value", "returnDate"], "properties": {"returnDate": {"type": "string"}, "value": {"type": "number", "format": "float64"}}}, "565347764_fields": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields . 565347764 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"__Updated": {"$ref": "#/definitions/1746819597___Updated"}, "announcements": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3193457757_announcements"}}}}, "568668110_text": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text . 568668110 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["hint", "content", "label"], "properties": {"content": {"$ref": "#/definitions/**********_content"}, "hint": {"$ref": "#/definitions/**********_hint"}, "label": {"$ref": "#/definitions/1693306878_label"}}}, "581687286_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/investment/allocations,METHOD:GET,PARAMETER:http-rsp-body->>* . 581687286 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["lastUpdateDate", "optionCode", "assetAllocations"], "properties": {"assetAllocations": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/518809641_assetAllocations"}}, "lastUpdateDate": {"type": "string"}, "optionCode": {"type": "number", "format": "float64"}}}, "*********_sitecore": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/select-account,METHOD:POST,PARAMETER:http-rsp-body->sitecore . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["route"], "properties": {"route": {"$ref": "#/definitions/*********_route"}}}, "*********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-event-processor/notifications/trigger,METHOD:POST,PARAMETER:http-req-body . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["source"], "properties": {"source": {"type": "string"}}}, "*********_type": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-announcements->>*->fields->announcements->>*->fields->type . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "61374470_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments/reminders/send,METHOD:POST,PARAMETER:http-req-body . 61374470 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["bookings"], "properties": {"bookings": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/544601243_bookings"}}}}, "618804654_investmentFee": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->investmentFee . 618804654 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["percentage"], "properties": {"percentage": {"type": "number", "format": "float64"}}}, "*********_hint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->hint . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_changeLogDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/account/*/variance/history,METHOD:GET,PARAMETER:http-rsp-body->changeLogDetails->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["date", "referenceId", "requestedBy", "status", "task"], "properties": {"date": {"type": "string"}, "referenceId": {"type": "string"}, "requestedBy": {"type": "string"}, "status": {"type": "string"}, "task": {"type": "string"}}}, "*********_addressDetails": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/personal-details,METHOD:GET,PARAMETER:http-rsp-body->addressDetails->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["addressline2", "addressline1", "addressUsageCode", "countryName", "addressPostcode", "state", "addressLocality"], "properties": {"addressLocality": {"type": "string"}, "addressPostcode": {"type": "string"}, "addressUsageCode": {"type": "string"}, "addressline1": {"type": "string"}, "addressline2": {"type": "string"}, "countryName": {"type": "string"}, "state": {"type": "string"}}}, "********_data": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts/*/investment/details,METHOD:GET,PARAMETER:http-rsp-body->data->>* . ******** : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["current"], "properties": {"current": {"$ref": "#/definitions/**********_current"}}}, "*********_insuranceDetails": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/application,METHOD:POST,PARAMETER:http-req-body->insuranceDetails . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"death": {"$ref": "#/definitions/1145118137_death"}, "insuranceQuestions": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_insuranceQuestions"}}, "ip": {"$ref": "#/definitions/1589216311_ip"}, "occupationRating": {"type": "string"}, "premiums": {"$ref": "#/definitions/2361654654_premiums"}, "tpd": {"$ref": "#/definitions/3616048921_tpd"}}}, "696937117_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/identity/login-history,METHOD:GET,PARAMETER:http-rsp-body->>* . 696937117 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["timestamp", "eventName", "memberNumber", "id", "system"], "properties": {"eventName": {"type": "string"}, "id": {"type": "string"}, "memberNumber": {"type": "string"}, "system": {"type": "string"}, "timestamp": {"type": "string"}}}, "*********_headingContent": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingContent . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_death": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->death . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["coverAmount", "units", "eligibility"], "properties": {"coverAmount": {"type": "number", "format": "float64"}, "eligibility": {"type": "boolean"}, "units": {"type": "number", "format": "float64"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/insurance,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["incomeProtectionCover", "totalAndPermanentDisablementBenefit", "deathBenefit", "isAccountInClaim", "disablementTemporaryIncapacityBenefit"], "properties": {"deathBenefit": {"$ref": "#/definitions/**********_deathBenefit"}, "disablementTemporaryIncapacityBenefit": {}, "incomeProtectionCover": {"$ref": "#/definitions/**********_incomeProtectionCover"}, "isAccountInClaim": {"type": "boolean"}, "totalAndPermanentDisablementBenefit": {"$ref": "#/definitions/**********_totalAndPermanentDisablementBenefit"}}}, "*********_headingHint": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingHint . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/members/interactions,METHOD:POST,PARAMETER:http-req-body . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["customerContact", "startDateTime", "callType", "interactionId", "agentId", "emailSubject", "wrapUpCode", "InteractionUrl", "mediaType", "endDateTime"], "properties": {"InteractionUrl": {"type": "string"}, "agentId": {"type": "string"}, "callType": {"type": "string"}, "customerContact": {"type": "string"}, "emailSubject": {"type": "string"}, "endDateTime": {"type": "string"}, "interactionId": {"type": "string"}, "mediaType": {"type": "string"}, "memberIdentifier": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_memberIdentifier"}}, "startDateTime": {"type": "string"}, "topicCodes": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3138481986_topicCodes"}}, "wrapUpCode": {"type": "string"}}}, "*********_latestReturn": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/investment/*/performance/*,METHOD:GET,PARAMETER:http-rsp-body->latestReturn . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["returnDate", "objectivePeriod", "actualReturn"], "properties": {"actualReturn": {"type": "number", "format": "float64"}, "objectivePeriod": {"type": "number", "format": "float64"}, "returnDate": {"type": "string"}}}, "*********_ip": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/account/personal,METHOD:POST,PARAMETER:http-req-body->insurance->ip . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["requested", "coverAmount", "units", "eligibility"], "properties": {"coverAmount": {"type": "number", "format": "float64"}, "eligibility": {"type": "boolean"}, "requested": {"type": "boolean"}, "units": {"type": "number", "format": "float64"}}}, "*********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/partner-onboarding/members/anonymous-search/,METHOD:POST,PARAMETER:http-req-body . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["givenNames", "surname", "mobileNumber", "emailAddress", "postcode"], "properties": {"emailAddress": {"type": "string"}, "givenNames": {"type": "string"}, "mobileNumber": {"type": "string"}, "postcode": {"type": "string"}, "surname": {"type": "string"}}}, "846662735_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/agents/chat-availability,METHOD:GET,PARAMETER:http-rsp-body . 846662735 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["emergencySchedule", "chatSchedule", "agentStatus"], "properties": {"agentStatus": {"type": "boolean"}, "chatSchedule": {"type": "boolean"}, "emergencySchedule": {"type": "boolean"}}}, "*********_content": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->subHeading->content . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/personal-details,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["contactDetails", "personDetails", "addressDetails"], "properties": {"addressDetails": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/*********_addressDetails"}}, "contactDetails": {"$ref": "#/definitions/**********_contactDetails"}, "personDetails": {"$ref": "#/definitions/**********_personDetails"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:401,CONTENTTYPE:application/json,ENDPOINT:/v1/salesforce-crm/appointments/reminders/send,METHOD:POST,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"message": {"type": "string"}, "statusCode": {"type": "number", "format": "float64"}}}, "873742939_headingLabel": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->toolTip->headingLabel . 873742939 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_label": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/*/retrieve,METHOD:GET,PARAMETER:http-rsp-body->sitecore->route->placeholders->jss-app-content->>*->fields->text->label . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"value": {"type": "string"}}}, "*********_members": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/accounts,METHOD:GET,PARAMETER:http-rsp-body->members->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["contactId", "accounts", "memberAliasId", "memberNumber"], "properties": {"accounts": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/**********_accounts"}}, "contactId": {"type": "string"}, "memberAliasId": {"type": "string"}, "memberNumber": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/content/retrieve,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["sitecore"], "properties": {"sitecore": {"$ref": "#/definitions/**********_sitecore"}}}, "*********_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/v1/mol-api/utility/address/complete,METHOD:POST,PARAMETER:http-req-body . ********* : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object"}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/employer/*/member-search,METHOD:POST,PARAMETER:http-rsp-body->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["staffMember", "employmentTerminationDate", "employerNumber", "givenNames", "<PERSON><PERSON>ame", "payrollNumbers", "dateOfBirth", "fund", "memberContributionPreTaxRate", "accountStatus", "memberNumber", "product", "accountNumber", "memberContributionPostTaxRate", "uniqueAccountId"], "properties": {"accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "dateOfBirth": {"type": "string"}, "employerNumber": {"type": "string"}, "employmentTerminationDate": {"type": "string"}, "familyName": {"type": "string"}, "fund": {"type": "string"}, "givenNames": {"type": "string"}, "memberContributionPostTaxRate": {"type": "number", "format": "float64"}, "memberContributionPreTaxRate": {"type": "number", "format": "float64"}, "memberNumber": {"type": "string"}, "payrollNumbers": {"type": "string"}, "product": {"type": "string"}, "staffMember": {"type": "string"}, "uniqueAccountId": {"type": "string"}}}, "*********_transactions": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/mobile-api/accounts/*/transactions-activity,METHOD:GET,PARAMETER:http-rsp-body->transactionActivities->>*->transactions->>* . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["amount", "component", "description", "date"], "properties": {"amount": {"type": "number", "format": "float64"}, "component": {}, "date": {"type": "string"}, "description": {"type": "string"}}}, "*********_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/v1/eol-api/alias,METHOD:GET,PARAMETER:http-rsp-body . ********* : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["externalId", "identifier"], "properties": {"externalId": {"type": "string"}, "identifier": {"type": "string"}}}}}