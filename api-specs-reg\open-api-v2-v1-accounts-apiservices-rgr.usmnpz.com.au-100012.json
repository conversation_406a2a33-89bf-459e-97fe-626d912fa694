{"swagger": "2.0", "info": {"title": "Accounts API", "version": "1.0", "description": "API for managing member account details."}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1/accounts", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/super/{memberAliasId}/salary-history": {"get": {"description": "Gets salary history details for a member account.", "operationId": "getSalaryHistory", "summary": "Gets salary history details for a member account.", "tags": ["accounts"], "parameters": [{"name": "memberAliasId", "in": "path", "description": "Unisuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "startDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Start date", "type": "string", "format": "date"}, {"name": "endDate", "in": "query", "description": "Format - date (as full-date in RFC3339). End date.", "type": "string", "format": "date"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/SalaryHistory"}}, "examples": {"application/json": [{"employerNumber": "string", "employerName": "string", "effectiveDate": "string", "confirmedSalary": "string", "onTargetExpectedSalary": "string", "pointOfTimeSalary": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{memberAliasId}/service-history": {"get": {"description": "Gets the service history details for a member account.", "operationId": "getServiceHistory", "summary": "Gets the service history details for a member account.", "tags": ["accounts"], "parameters": [{"name": "memberAliasId", "in": "path", "description": "Unisuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "startDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Start date", "type": "string", "format": "date"}, {"name": "endDate", "in": "query", "description": "Format - date (as full-date in RFC3339). End date.", "type": "string", "format": "date"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/ServiceHistory"}}, "examples": {"application/json": [{"employerNumber": "string", "employerName": "string", "startDate": "string", "endDate": "string", "serviceFraction": "string", "reasonCode": "string", "halfContributions": "string", "prePostTaxContributions": "string", "eligibility": "string", "fundChoice": "string", "permanent": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/transactions": {"get": {"description": "Gets the transaction history detail for a member account.", "operationId": "getTransactions", "summary": "Gets the transaction history detail for a member account.", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "Unisuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "startDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Start date", "type": "string", "format": "date"}, {"name": "endDate", "in": "query", "description": "Format - date (as full-date in RFC3339). End date.", "type": "string", "format": "date"}, {"name": "maximumTransactions", "in": "query", "description": "Maximum transactions.", "type": "integer", "default": 500}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/TransactionHistory"}}, "examples": {"application/json": [{"effectiveDate": "string", "transactionType": "string", "description": "string", "amount": "string", "component": "string", "balance": "string", "transactionCode": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/notional-taxed-contributions": {"get": {"description": "Gets the notional .", "operationId": "getGetNotionalTaxedContributions", "summary": "Gets the notional .", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/NotionalTaxedContribution"}}, "examples": {"application/json": [{"startDate": "string", "endDate": "string", "newEntrantRate": 0, "fullTimeEquivalentSalary": 0, "contributionDays": 0, "definedBenefitAmount": 0, "ntcTotal": 0}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super": {"post": {"description": "Operation for creating personal or shell account.", "operationId": "CreateAccount", "summary": "Operation for creating personal or shell account.", "tags": ["accounts"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "createAccountRequest", "in": "body", "schema": {"$ref": "#/definitions/CreateAccountRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{referenceId}/account-creation-status": {"get": {"description": "Gets the account status details for a Unique confirmation number generated while creating account .", "operationId": "createAccountStatus", "summary": "Gets the account status details for a Unique confirmation number generated while creating account .", "tags": ["accounts"], "parameters": [{"name": "referenceId", "in": "path", "description": "Unique confirmation number generated while creating account", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/accountCreationStatus"}, "examples": {"application/json": {"memberNumber": "string", "memberStatus": "string", "fund": "string", "accountNumber": "string", "uniqueAccountId": "string", "requestStatus": "string", "requestedDateTime": "string", "completedDateTime": "string", "unisuperIdentifier": "string", "isMOLRegistered": true, "username": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "No Data Found", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "408": {"description": "Request Timeout"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error"}}}}, "/super/search/": {"post": {"description": "Operation for searching an account in Acurity.", "operationId": "SearchSuperAccount", "summary": "Operation for searching an account in Acurity.", "tags": ["accounts"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "searchSuperAccountRequest", "in": "body", "schema": {"$ref": "#/definitions/SearchSuperAccountRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded, Search result returned", "schema": {"$ref": "#/definitions/SearchSuperAccountResponse"}, "examples": {"application/json": [{"memberNumber": "string", "productCode": "string", "productDescription": "string", "preTaxMemberContribution": "string", "postTaxMemberContribution": "string", "memberElectedContributionRateDesc": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "503": {"description": "The request cannot be served - Acurity is offline"}}}}, "/super/{memberNumberAliasId}/insurance": {"get": {"description": "Gets insurance details for a member account.", "operationId": "getInsurance", "summary": "Gets insurance details for a member account.", "tags": ["accounts"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "Unisuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/InsuranceDetails"}, "examples": {"application/json": {"memberNumber": 0, "deathInsuranceCoverUnits": 0, "deathInsuranceCoverAmount": 0, "totalAndPermanentDisabilityInsuranceCoverUnits": 0, "totalAndPermanentDisabilityInsuranceCoverAmount": 0, "incomeProtectionInsuranceCoverUnits": 0, "incomeProtectionInsuranceCoverAmount": 0, "incomeProtectionWaitingPeriod": "string", "incomeProtectionBenefitPeriod": "string", "inbuiltTemporaryIncapacityBenefit": 0, "accumulationBalance": 0, "definedBenefitBalance": 0, "totalDefinedBenefitDeathBenefit": 0, "inbuiltDeathBenefit": 0, "totalAccumulationDeathBenefit": 0, "totalTPDBenefit": 0, "isIncomeProtectionAutoUpdateOut": true, "incomeProtectionAutoUpdateIneligibilityReason": "string", "insuranceEffectiveDate": "string", "isPending": true, "deathCoverEligibilityCode": "string", "deathCoverEligibilityDescription": "string", "incomeProtectionEligibilityCode": "string", "incomeProtectionEligibilityDescription": "string", "tpdCoverEligibilityCode": "string", "tpdCoverEligibilityDescription": "string", "accountBalanceEffectiveDate": "string", "sftInsuranceCode": "ACSF", "sftGrandFatheredCode": "A: Z Fixed Death and TPD; B: Z Fixed Income Protection"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/notice-of-intent": {"post": {"description": "Create member's notice of intent", "operationId": "ManageNoi", "summary": "Create member's notice of intent", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "createNoiRequest", "in": "body", "schema": {"$ref": "#/definitions/CreateNoiRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{memberNumberAliasId}/insurance/cover-cancellation": {"put": {"description": "Cancels external insurance for member's super (only) account and allows member to completely opt out from external insurance..", "operationId": "cancelInsuranceCover", "summary": "Cancels external insurance for member's super (only) account and allows member to completely opt out from external insurance..", "tags": ["super"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "UniSuper issued member number (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "cancelInsuranceCoverRequest", "in": "body", "schema": {"$ref": "#/definitions/CancelInsuranceCoverRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request accepted, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/balance-history": {"get": {"description": "Gets balance history for an account.", "operationId": "getAccountBalanceHistory", "summary": "Gets balance history for an account.", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "startDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Balance History Start Date", "required": true, "type": "string", "format": "date"}, {"name": "isCurrentAccount", "in": "query", "description": "Only search in current account. Default is true.", "type": "boolean"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/AccountBalanceHistory"}}, "examples": {"application/json": [{"effectiveDate": "string", "definedBenefitAmount": 0, "accumulationAmount": 0, "totalBalance": 0}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension": {"post": {"description": "Submit Flexi Pension Application", "operationId": "submitPensionApplication", "summary": "Submit Flexi Pension Application", "tags": ["pension"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "submitPensionApplicationRequest", "in": "body", "schema": {"$ref": "#/definitions/SubmitPensionApplicationRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Accepted, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/withdrawal": {"post": {"description": "Withdraw lump sum amount from Flexi Pension account. Request nominates a PDF rendering template and data for PDF generation.", "operationId": "withdrawFlexiPensionAmount", "summary": "Withdraw lump sum amount from Flexi Pension account. Request nominates a PDF rendering template and data for PDF generation.", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "withdrawPensionAmountRequest", "in": "body", "schema": {"$ref": "#/definitions/WithdrawPensionAmountRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Accepted, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/bank-details": {"get": {"description": "Get Pensioner Bank Details", "operationId": "getPensionBankDetails", "summary": "Get Pensioner Bank Details", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"items": {"$ref": "#/definitions/PensionBankDetails"}}, "examples": {"application/json": {}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "put": {"description": "Updates pension bank account details", "operationId": "updateBankDetails", "summary": "Updates pension bank account details", "tags": ["bankdetails"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-SendCommunication", "in": "header", "description": "flag is set if a communication needs to be sent to member", "required": true, "type": "boolean"}, {"name": "pension-accountAliasId-Bank-detailsPutRequest-3", "in": "body", "schema": {"type": "array", "items": {"$ref": "#/definitions/PensionBankDetails"}, "example": [{"accountName": "string", "accountNumber": "string", "bsb": "string", "institutionName": "string", "institutionBranchName": "string", "percentageAllocated": 0, "bankCode": "string", "lastModifiedDateTime": "string"}]}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded,no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/third-party-authority/{documentSystemIdentifier}": {"put": {"description": "This service allows to revoke the third party authority for a member account.", "operationId": "revokeThirdPartyAuthorityDetails", "summary": "This service allows to revoke the third party authority for a member account.", "tags": ["third party authority"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "documentSystemIdentifier", "in": "path", "description": "Document System Identifier. This value is generally obtained from Retrieve third party authority service", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "revokeThirdPartyAuthorityRequest", "in": "body", "schema": {"$ref": "#/definitions/RevokeThirdPartyAuthorityRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/third-party-authority": {"post": {"description": "This service allows to nominate the third party authority for a member account.", "operationId": "nominateThirdPartyAuthorityDetails", "summary": "This service allows to nominate the third party authority for a member account.", "tags": ["third party authority"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "nominateThirdPartyAuthorityRequest", "in": "body", "schema": {"$ref": "#/definitions/NominateThirdPartyAuthorityRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/third-party-authority": {"get": {"description": "Gets the third party authority detail for a member account.", "operationId": "getThirdPartyAuthorityDetails", "summary": "Gets the third party authority detail for a member account.", "tags": ["third party authority"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/ThirdPartyAuthorityDetails"}}, "examples": {"application/json": [{"documentIdentifier": "string", "documentSignedDate": "string", "dateRevoked": "string", "authorityDetails": [{"relationshipToMember": "string", "authorisedThirdPartyName": "string", "companyName": "string", "authorisedRepresentativeNumber": "string"}]}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/communication-preferences": {"get": {"description": "Gets Communication preference details for a member account - Email, Post, etc..", "operationId": "getCommunicationPreferences", "summary": "Gets Communication preference details for a member account - Email, Post, etc..", "tags": ["communicationPreferences"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "Account alias <PERSON><PERSON>", "required": true, "type": "string"}, {"name": "CommunicationCategory", "in": "query", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/CommunicationPreferenceDetails"}}, "examples": {"application/json": [{"communicationCategory": "string", "communicationPreference": "EMAIL_PERSONAL", "effectiveDate": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "patch": {"description": "Managing the Communication Preference..i.e Post or Email for the member account.", "operationId": "updateCommunicationPreferences", "summary": "Managing the Communication Preference..i.e Post or Email for the member account.", "tags": ["communicationPreferences"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "Account alias <PERSON><PERSON>", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "communicationPreferenceDetails", "in": "body", "schema": {"$ref": "#/definitions/CommunicationPreferenceDetails"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request accepted"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{memberNumberAliasId}/rollover-outcome-communication": {"post": {"description": "Used to notify member once funds have been received to the account as part of a rollover. Member level mobile number is used to send SMS (only) notification.", "operationId": "rolloverOutcomeCommunication", "summary": "Used to notify member once funds have been received to the account as part of a rollover. Member level mobile number is used to send SMS (only) notification.", "tags": ["super"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "UniSuper issued member number (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "rolloverOutcomeCommunicationRequest", "in": "body", "schema": {"$ref": "#/definitions/RolloverOutcomeCommunicationRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/benefit-nominations": {"get": {"description": "Gets the current and pending (revisionary) benefit nominations for the specified account", "operationId": "getBenefitNominations", "summary": "Gets the current and pending (revisionary) benefit nominations for the specified account", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. \nClient-generated GUID with no decoration such as curly braces (eg.\n'9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/BenefitNomination"}, "examples": {"application/json": {"nominationType": "NonBinding", "beneficiaries": [{"percentage": 100, "relationship": "Spouse", "beneficiaryCategory": "Current", "effectiveDate": "string", "expiryDate": "string", "personalDetails": {"personTitle": "Mr", "givenName": "<PERSON>", "familyName": "<PERSON><PERSON>", "gender": "Male", "sexAtBirth": "Male", "dateOfBirth": "string", "address": {"addressLine1": "string", "addressLine2": "string", "suburb": "string", "state": "string", "postCode": "string", "country": "string"}}, "emailDetails": {"electronicMailUsageCode": "Unknown", "electronicMailAddress": "<EMAIL>"}}]}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "\nForbidden. The request is understood, but it has been refused or\naccess is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "\nThe request cannot be served due to the rate limit having been\nexhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "put": {"description": "Upserts the benefit nominations for an account. This is straight through, i.e. UniSuper systems are updated without human intervention in happy path scenario", "operationId": "submitBenefitNominations", "summary": "Upserts the benefit nominations for an account. This is straight through, i.e. UniSuper systems are updated without human intervention in happy path scenario", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. \nClient-generated GUID with no decoration such as curly braces (eg.\n'9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "benefitNominationRequest", "in": "body", "schema": {"$ref": "#/definitions/BenefitNominationRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "\nForbidden. The request is understood, but it has been refused or\naccess is not allowed"}, "429": {"description": "\nThe request cannot be served due to the rate limit having been\nexhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/details-extended": {"get": {"description": "This service will return the details of some special kind of account - TI and TPD which is currently not present in ODS common schemas", "operationId": "getPensionExtendedDetails", "summary": "This service will return the details of some special kind of account - TI and TPD which is currently not present in ODS common schemas", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "includeMembersForSuperPayments", "in": "query", "description": "IncludeMembersForSuperPayments", "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/PensionExtendedDetails"}, "examples": {"application/json": {"memberNumber": "string", "dob": "string", "webRegistrationStatus": "string", "accountType": "string", "includeMembersForSuperPayments": "string", "accountStatus": "string", "isPaymentSuspended": true}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/drawdown-details": {"put": {"description": "Updates the drawdown method of a pension account", "operationId": "UpdateDrawdownMethod", "summary": "Updates the drawdown method of a pension account", "tags": ["drawdown"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "drawdownMethodDetails", "in": "body", "schema": {"$ref": "#/definitions/DrawdownMethodDetails"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "get": {"description": "Get Pensioner drawdown details", "operationId": "getPensionDrawdownDetails", "summary": "Get Pensioner drawdown details", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/DrawdownMethodDetails"}, "examples": {"application/json": {"drawdownMethod": "string", "drawdownChoiceOptions": [{"investmentAssetClass": "string", "investmentAssetName": "string", "investmentAssetCode": "string", "investmentDrawdownOrder": 0, "investmentAssetAllocation": "string"}]}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/details": {"get": {"description": "Gets details for an account.", "operationId": "getAccountDetails", "summary": "Gets details for an account.", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "isCurrentAccount", "in": "query", "description": "Only search in current account. Default is true.", "type": "boolean"}, {"name": "shouldRefreshBalance", "in": "query", "description": "If true Request web quote to refresh balance", "type": "boolean"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/AccountBalanceDetail"}, "examples": {"application/json": {"accountTypeCode": "string", "AccountDescription": "string", "userRegistrationDate": "string", "accountStatus": "string", "membershipType": "string", "sftMemberIdentifier": "string", "sftOriginatingFundCode": "AC", "sftAccountIdentifier": "string", "sftCommencementDate": "string", "accountBalance": {"totalBalance": 0, "balanceEffectiveDate": "string", "accountCommencementDate": "string", "allowNonConcessionalContribution": 0, "definedBenefitAmount": 0, "accumulationAmount": 0, "preservedAmount": 0, "nonPreservedRestrictedAmount": 0, "nonPreservedUnrestrictedAmount": 0, "threeYearAverageBenefitSalary": 0, "fiveYearAverageBenefitSalary": 0, "lumpSumFactor": 0, "averageContributionFactor": 0, "pre2015ContributingService": 0, "post2015ContributingService": 0, "averageServiceFractionFundService": 0, "preDecember2000ContributingServiceFundService": 0, "qualifyingBenefitServiceFundService": 0, "relevantAverageServiceFractionFundService": 0, "pre2015ContributingServiceAndDisablementReduction": 0, "post2015ContributingServiceAndDisablementReduction": 0, "qualifyingBenefitServiceReduction": 0, "preDecember2000ContributingServiceReduction": 0, "additionalAccumulationContributionComponent": 0, "additionalAccumulationContributionComponentReduction": 0, "benefitServiceBefore1January2002": 0, "isTransitionToRetirement": true, "hasBenefitC34Participation": true, "benefitC34StartDate": "string", "benefitYearsPostC34": 0}}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/investments": {"get": {"description": "Gets investment details for a member account.", "operationId": "getInvestmentDetails", "summary": "Gets investment details for a member account.", "tags": ["accounts", "investments"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "investmentStrategy", "in": "query", "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Client-generated GUID with no decoration such as curly braces (eg. '5C4D50EE-2D46-4CD3-8152-34347DC9F2B0').", "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/InvestmentProfileDetails"}}, "examples": {"application/json": [{"investmentStrategy": "Current/Future/Rollover", "investmentStrategyEffectiveDate": "string", "investmentStrategyStatus": "Confirmed", "investmentOptionProfile": [{"investmentAssetClass": "pre-mixed/self-select", "investmentAssetName": "Sustainable Balanced", "investmentAssetCode": 14, "investmentAssetPercentage": 100, "investmentAssetBalanceAmount": 100}]}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "post": {"description": "Submit request to update member's investment choice", "operationId": "UpdateInvestmentChoice", "summary": "Submit request to update member's investment choice", "tags": ["accounts", "investments"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Client-generated GUID with no decoration such as curly braces (eg. '5C4D50EE-2D46-4CD3-8152-34347DC9F2B0').", "type": "string"}, {"name": "submissionDate", "in": "header", "description": "Format - date (as full-date in RFC3339). UpdateInvestment submission date", "type": "string", "format": "date"}, {"name": "accountAliasId-InvestmentsPostRequest-4", "in": "body", "schema": {"type": "array", "items": {"$ref": "#/definitions/UpdateInvestmentChoiceRequest"}, "example": [{"investmentStrategy": "string", "requestedInvestmentOption": [{"assetCode": 11, "assetPercentage": 42}]}]}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/InvestmentChoiceResponse"}, "examples": {"application/json": {"effectiveDate": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/investments/switch-history": {"get": {"description": "Gets investment details for a member account.", "operationId": "getInvestmentSwitchHistoryDetails", "summary": "Gets investment details for a member account.", "tags": ["accounts", "investments"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "DateFrom", "in": "query", "description": "Format - date (as full-date in RFC3339).", "type": "string", "format": "date"}, {"name": "DateTo", "in": "query", "description": "Format - date (as full-date in RFC3339).", "type": "string", "format": "date"}, {"name": "NumberOfRecords", "in": "query", "type": "integer"}, {"name": "investmentStrategy", "in": "query", "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/SwitchHistoryDetails"}}, "examples": {"application/json": [{"requestedDate": "string", "effectiveDate": "string", "switchStrategy": "Current", "switchStatus": "Confirmed", "groupOffset": 1, "investmentOptionProfile": [{"investmentAssetClass": "pre-mixed/self-select", "investmentAssetName": "Sustainable Balanced", "investmentAssetCode": 14, "investmentAssetPercentage": 100, "investmentAssetBalanceAmount": 100}]}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/payment-details": {"get": {"description": "Get Pensioner Payment Details", "operationId": "getPensionPaymentDetails", "summary": "Get Pensioner Payment Details", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/PensionPaymentDetails"}, "examples": {"application/json": {"pensionInstalments": [{"paymentFrequency": "string", "instalmentPaymentAmount": 0}], "nextPensionPaymentDate": "string", "annualPension": 0, "pensionStartDate": "string", "tapTerm": 0, "pensionPaymentPaidForFinancialYear": 0, "pensionPaymentStartOfFinancialYearDate": "string", "pensionPaymentEndOfFinancialYearDate": "string", "annualTaxFreeComponent": 0, "minimumPensionValue": 0, "maximumPensionValue": 0, "pensionPaymentStatus": "string", "isPaymentSuspended": true, "processingDate": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{memberNumberAliasId}/rollover-in": {"post": {"description": "This service is used my a member to submit a rollover request, to roll over funds from another fund into a UniSuper account.", "operationId": "SubmitRolloverIn", "summary": "This service is used my a member to submit a rollover request, to roll over funds from another fund into a UniSuper account.", "tags": ["super"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "UniSuper issued member number (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg.'9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Client-generated GUID with no decoration such as curly braces (eg. '5C4D50EE-2D46-4CD3-8152-34347DC9F2B0').", "type": "string"}, {"name": "submitRolloverInRequest", "in": "body", "schema": {"$ref": "#/definitions/SubmitRolloverInRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Accepted"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{MemberNumberAliasId}/rollover-in/search": {"post": {"description": "Retrieve <PERSON><PERSON>in Requests", "operationId": "RetrieveRolloverinRequest", "summary": "Retrieve <PERSON><PERSON>in Requests", "tags": ["accounts"], "parameters": [{"name": "MemberNumberAliasId", "in": "path", "description": "Format - uuid. UniSuper issued member identifier (alias)", "required": true, "type": "string", "format": "uuid"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "retrieveRolloverinRequests", "in": "body", "schema": {"$ref": "#/definitions/RetrieveRolloverinRequests"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/RolloverinRequests"}}, "examples": {"application/json": [{"sourceFund": {"usi": "**************", "memberNumber": "************"}, "isDuplicate": true, "submittedDate": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/insurance/cover-retention": {"put": {"description": "Updates members super account to retain their insurance cover for their super account (only), even though the account qualifies to have their insurance cover automatically cancelled under the Protecting Your Super legislation.", "operationId": "updateInsuranceCoverChoice", "summary": "Updates members super account to retain their insurance cover for their super account (only), even though the account qualifies to have their insurance cover automatically cancelled under the Protecting Your Super legislation.", "tags": ["super"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "retainInsuranceCoverRequest", "in": "body", "schema": {"$ref": "#/definitions/RetainInsuranceCoverRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/insurance/ip-auto-update": {"delete": {"description": "Opt out of Income Protection Insurance Auto Update.", "operationId": "IPAutoUpdateOptOut", "summary": "Opt out of Income Protection Insurance Auto Update.", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "Format - uuid. Unisuper issued account identifier (alias)", "required": true, "type": "string", "format": "uuid"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '432150EE-2D56-4CD3-8152-34347DC12345').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-OriginatingSystem", "in": "header", "description": "Lodgement orignating system", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"202": {"description": "Request accepted, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/payment-election": {"put": {"description": "Updates a pension accounts payment frequency", "operationId": "UpdatePaymentElection", "summary": "Updates a pension accounts payment frequency", "tags": ["paymentElection"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "paymentElections", "in": "body", "schema": {"$ref": "#/definitions/PaymentElections"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no reponse body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{memberNumberAliasId}/notice-of-intent/status": {"put": {"description": "Send the reviewed status of a notice of intent application to Acurity.", "operationId": "SubmitNOIStatus", "summary": "Send the reviewed status of a notice of intent application to Acurity.", "tags": ["accounts"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "UniSuper issued member number (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-OriginatingSystem", "in": "header", "description": "Format - string. Name of the system originating the request.", "required": true, "type": "string", "format": "string"}, {"name": "submitNOIStatusRequest", "in": "body", "schema": {"$ref": "#/definitions/SubmitNOIStatusRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}, "get": {"description": "Get Notice of Intent Claim Status", "operationId": "GetNOIStatus", "summary": "Get Notice of Intent Claim Status", "tags": ["accounts"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "UniSuper issued member number (alias)", "required": true, "type": "string"}, {"name": "currentFinancialYear", "in": "query", "description": "Is the request for current financial year", "required": true, "type": "boolean"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/NoticeOfIntentClaimStatusResponse"}}, "examples": {"application/json": [{"referenceNmber": "11288", "accountNumber": "F14815", "requestStatus": "Completed", "requestDate": "string", "effectiveDate": "string", "financialYear": "string", "presentClaimAmount": 6619.82, "previousClaimAmount": 2.45, "claimAmount": 2.25, "variance": 0, "totalClaimAmount": 6624.52, "documentName": "AD_UNIS_M_F14815_20210421_A.PDF"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{memberNumberAliasId}/contribution-flexibility-details": {"get": {"description": "Retrieve contribution flexibility details for the member", "operationId": "retrieveContributionFlexibilityDetails", "summary": "Retrieve contribution flexibility details for the member", "tags": ["accounts"], "parameters": [{"name": "memberNumberAliasId", "in": "path", "description": "Unisuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "effectiveDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Effective date for the details", "type": "string", "format": "date"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/MemberContributionFlexibilityDetails"}, "examples": {"application/json": {"title": "MR", "familyName": "RANKIN", "givenName": "JAMES PETER HARRY", "otherGivenNames": "string", "birthDate": "string", "dateJoinedFund": "string", "contFlexEffectiveDate": "string", "ContFlexEmployerRatePercentage": 17, "contFlexMemberRate": "0.00% Post-Tax / 0.00% Pre-Tax"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{accountAliasId}/transactions/unapplied": {"get": {"description": "Gets the Unapplied Transaction Details for a member account.", "operationId": "getUnappliedTransactions", "summary": "Gets the Unapplied Transaction Details for a member account.", "tags": ["accounts"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/UnappliedTransactionDetails"}}, "examples": {"application/json": [{"accountBalanceDate": "string", "investmentStrategyID": 12, "investmentStrategyName": "International Shares", "totalInterestToDate": 5802.45, "interestFromDate": "2022-07-01", "previousFYInterestToDate": 1234.56, "currentFYInterestToDate": 4567.89}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/centrelink-schedule": {"post": {"description": "Generates the Centrelink Schedule in Acurity for pension members.", "operationId": "generateCentrelinkSchedule", "summary": "Generates the Centrelink Schedule in Acurity for pension members.", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "centrelinkScheduleRequest", "in": "body", "schema": {"$ref": "#/definitions/CentrelinkScheduleRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/bank-details": {"get": {"description": "Gets bank account details used in previous transactions for the given super account.", "operationId": "GetSuperBankAccountDetails", "summary": "Get Super Bank Account Details", "tags": ["super"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "Super Account Identifier (alias)", "required": true, "type": "string"}, {"name": "periodStartDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Period start date since when bank accounts will be retrieved. If not passed, this criterion wont be used.", "type": "string", "format": "date"}, {"name": "periodEndDate", "in": "query", "description": "Format - date (as full-date in RFC3339). Period end date till when bank accounts will be retrieved. If not passed, this criterion wont be used.", "type": "string", "format": "date"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/SuperBankAccountDetail"}}, "examples": {"application/json": [{"accountName": "string", "accountNumber": "string", "bsb": "string", "institutionName": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/super/{accountAliasId}/withdrawal": {"post": {"description": "Submits a new Super Withdrawal Request for straight-through processing.", "operationId": "SubmitSuper<PERSON><PERSON><PERSON>", "summary": "Submit Super Withdrawal", "tags": ["Super Withdrawal"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued account identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "submitSuperWithdrawalRequest", "in": "body", "schema": {"$ref": "#/definitions/SubmitSuperWithdrawalRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/search": {"post": {"description": "Looks up an account and returns the details", "operationId": "SearchAccount", "summary": "Account Search", "tags": ["Account Search"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "type": "string", "format": "uuid"}, {"name": "searchAccountRequest", "in": "body", "schema": {"$ref": "#/definitions/SearchAccountRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/SearchAccountResponse"}}, "examples": {"application/json": [{"items": [{"memberNumber": "string", "uniqueAccountID": "string", "accountTypeCode": "string", "accountStatus": "string", "accountTypeSummaryName": "string", "title": "string", "givenNames": "string", "familyName": "string", "dateOfBirth": "string", "mobileNumber": "string", "homeEmailAddress": "string", "alternateEmailAddress": "string", "username": "string", "matchDescription": "string"}]}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/pension/{accountAliasId}/flexi-withdrawal": {"post": {"description": "Submits a new Flexi Pension Withdrawal Request for straight-through processing.", "operationId": "withdrawFlexiPension", "summary": "Withdraw Flexi Pension", "tags": ["pension"], "parameters": [{"name": "accountAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "flexiPensionWithdrawalRequest", "in": "body", "schema": {"$ref": "#/definitions/FlexiPensionWithdrawalRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Accepted, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood,but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}}, "definitions": {"RetrieveRolloverinRequests": {"type": "array", "items": {"$ref": "#/definitions/SourceFund"}, "example": [{"usi": "**************", "memberNumber": "************"}]}, "RolloverinRequests": {"type": "object", "properties": {"sourceFund": {"$ref": "#/definitions/SourceFund"}, "isDuplicate": {"type": "boolean"}, "submittedDate": {"format": "date-time", "type": "string"}}}, "SourceFund": {"type": "object", "properties": {"usi": {"type": "string", "example": "**************"}, "memberNumber": {"type": "string", "example": "************"}}}, "SalaryHistory": {"properties": {"employerNumber": {"description": "Employer number", "type": "string"}, "employerName": {"description": "Employer name", "type": "string"}, "effectiveDate": {"format": "date-time", "description": "Effective date", "type": "string"}, "confirmedSalary": {"description": "Confirmed salary", "type": "string"}, "onTargetExpectedSalary": {"description": "On Target Expected salary", "type": "string"}, "pointOfTimeSalary": {"description": "Point of time salary", "type": "string"}}}, "ServiceHistory": {"properties": {"employerNumber": {"description": "Employer number", "type": "string"}, "employerName": {"description": "Employer name", "type": "string"}, "startDate": {"format": "date-time", "description": "Start date", "type": "string"}, "endDate": {"format": "date-time", "description": "End date", "type": "string"}, "serviceFraction": {"description": "Service fraction", "type": "string"}, "reasonCode": {"description": "Reason code", "type": "string"}, "halfContributions": {"description": "Half contributions", "type": "string"}, "prePostTaxContributions": {"description": "Pre post tax contrbutionsr", "type": "string"}, "eligibility": {"description": "Eligibility", "type": "string"}, "fundChoice": {"description": "Fund choice", "type": "string"}, "permanent": {"description": "Permanent", "type": "string"}}}, "TransactionHistory": {"properties": {"effectiveDate": {"format": "date-time", "description": "Effective date", "type": "string"}, "transactionType": {"description": "Transaction type", "type": "string"}, "description": {"description": "Description", "type": "string"}, "amount": {"description": "Amount in dollars.", "type": "string"}, "component": {"description": "Component", "type": "string"}, "balance": {"description": "The running balance for the transactions.", "type": "string"}, "transactionCode": {"description": "Transaction Code", "type": "string"}}}, "TransactionCategory": {"enum": ["ALL", "Contribution"], "type": "string"}, "PersonalDetails": {"required": ["title", "firstName", "surname", "dateOfBirth"], "type": "object", "properties": {"title": {"description": "Member Title", "type": "string"}, "firstName": {"description": "Member First Name", "type": "string"}, "surname": {"description": "Member Last Name", "type": "string"}, "gender": {"description": "Member Gender", "type": "string"}, "sexAtBirth": {"$ref": "#/definitions/SexAtBirth"}, "dateOfBirth": {"format": "date", "description": "Member DOB", "type": "string"}, "tfn": {"description": "Member TFN", "type": "string"}}}, "ResidentialAddress": {"required": ["addressLine1", "suburb", "state", "postCode", "country"], "type": "object", "properties": {"addressLine1": {"description": "Address Line 1", "type": "string"}, "addressLine2": {"description": "Address Line 2", "type": "string"}, "suburb": {"description": "Suburb", "type": "string"}, "state": {"description": "State", "type": "string"}, "postCode": {"description": "Post Code", "type": "string"}, "country": {"description": "Country", "type": "string"}}}, "PostalAddress": {"required": ["addressLine1", "suburb", "state", "postCode", "country"], "type": "object", "properties": {"addressLine1": {"description": "Address Line 1", "type": "string"}, "addressLine2": {"description": "Address Line 2", "type": "string"}, "suburb": {"description": "Suburb", "type": "string"}, "state": {"description": "State", "type": "string"}, "postCode": {"description": "Post Code", "type": "string"}, "country": {"description": "Country", "type": "string"}}}, "ContactDetails": {"required": ["email"], "type": "object", "properties": {"email": {"format": "email", "description": "Member <PERSON><PERSON>dress", "type": "string"}, "mobile": {"description": "Member Mobile Number", "type": "string"}}}, "Eligibility": {"type": "object", "properties": {"claimEntitlementEligibility": {"type": "object", "properties": {"question": {"description": "question to determine claim entitlement eligibility", "type": "string"}, "answer": {"$ref": "#/definitions/EligibilityAnswer"}}}, "terminalIllnessEligibility": {"type": "object", "properties": {"question": {"description": "question to determine terminal entitlement eligibility", "type": "string"}, "answer": {"$ref": "#/definitions/EligibilityAnswer"}}}, "tpdEligibility": {"type": "object", "properties": {"question": {"description": "question to determine tpd entitlement eligibility", "type": "string"}, "answer": {"$ref": "#/definitions/EligibilityAnswer"}}}, "employmentEligibility": {"type": "object", "properties": {"question": {"description": "question to determine employment entitlement eligibility", "type": "string"}, "answer": {"$ref": "#/definitions/EligibilityAnswer"}}}, "occupationEligibility": {"type": "object", "properties": {"question": {"description": "question to determine occupation entitlement eligibility", "type": "string"}, "answer": {"$ref": "#/definitions/EligibilityAnswer"}}}}}, "DeathInsuranceCover": {"type": "object", "properties": {"insuranceCoverage": {"$ref": "#/definitions/InsuranceCoverage"}, "isUnitised": {"description": "Is Unitised", "type": "boolean"}, "loadingDetails": {"$ref": "#/definitions/InsuranceCoverLoadingDetails"}, "exclusionDetails": {"$ref": "#/definitions/InsuranceCoverExclusionDetails"}, "status": {"description": "Insurance Status", "type": "string"}, "eligibilityReason": {"$ref": "#/definitions/EligibilityReason"}}}, "SubmitRolloverInRequest": {"required": ["personalDetails", "transferringFundDetails"], "type": "object", "properties": {"personalDetails": {"required": ["personTitle", "<PERSON><PERSON>ame", "<PERSON><PERSON><PERSON>", "birthDate"], "type": "object", "properties": {"personTitle": {"type": "string", "example": "Mr"}, "familyName": {"type": "string", "example": "NORTH"}, "givenName": {"type": "string", "example": "<PERSON>"}, "birthDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string", "example": "1986-12-05"}}}, "transferringFundDetails": {"type": "array", "items": {"required": ["fundMemberIdentifier", "fundABN", "fundUSI", "fundProductIdentifier"], "type": "object", "properties": {"requestIdentifier": {"type": "string", "example": "a944fc79-1620-420c-8f73-cc052ba6769e"}, "fundMemberIdentifier": {"type": "string", "example": "1001403147"}, "fundABN": {"type": "string", "example": "***********"}, "fundUSI": {"type": "string", "example": "AET0100AU"}, "fundName": {"type": "string", "example": "Australian Ethical Super"}, "fundProductIdentifier": {"type": "string", "example": "Australian Ethical Retail Superannuation Fund"}}}}, "formDetails": {"type": "object", "properties": {"formIdentifier": {"type": "string", "example": "MOL_ROLLOVERIN"}, "lodgedDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string", "example": "2021-01-25"}, "authorisedIndicator": {"type": "boolean", "example": true}, "authorisedDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string", "example": "2021-01-25"}}}}, "example": {"personalDetails": {"personTitle": "Mr", "familyName": "NORTH", "givenName": "<PERSON>", "birthDate": "1986-12-05"}, "transferringFundDetails": [{"requestIdentifier": "a944fc79-1620-420c-8f73-cc052ba6769e", "fundMemberIdentifier": "1001403147", "fundABN": "***********", "fundUSI": "AET0100AU", "fundName": "Australian Ethical Super", "fundProductIdentifier": "Australian Ethical Retail Superannuation Fund"}], "formDetails": {"formIdentifier": "MOL_ROLLOVERIN", "lodgedDate": "2021-01-25", "authorisedIndicator": true, "authorisedDate": "2021-01-25"}}}, "DisabilityInsuranceCover": {"type": "object", "properties": {"insuranceCoverage": {"$ref": "#/definitions/InsuranceCoverage"}, "isUnitised": {"description": "Is Unitised", "type": "boolean"}, "loadingDetails": {"$ref": "#/definitions/InsuranceCoverLoadingDetails"}, "exclusionDetails": {"$ref": "#/definitions/InsuranceCoverExclusionDetails"}, "status": {"description": "Insurance Status", "type": "string"}, "eligibilityReason": {"$ref": "#/definitions/EligibilityReason"}}}, "IncomeProtectionInsuranceCover": {"type": "object", "properties": {"status": {"description": "Insurance Status", "type": "string"}, "eligibilityReason": {"$ref": "#/definitions/EligibilityReason"}, "insuranceCoverage": {"$ref": "#/definitions/InsuranceCoverage"}, "isUnitised": {"description": "Is Unitised", "type": "boolean"}, "loadingDetails": {"$ref": "#/definitions/InsuranceCoverLoadingDetails"}, "exclusionDetails": {"$ref": "#/definitions/InsuranceCoverExclusionDetails"}, "waitingPeriod": {"type": "object", "properties": {"waitingDays": {"description": "Waiting Days", "type": "string"}}}, "benefitDetails": {"type": "object", "properties": {"benefitPeriod": {"description": "Benefit Period", "type": "string"}}}}}, "InsuranceCoverage": {"type": "object", "properties": {"numberOfUnit": {"description": "Number of Insurance cover unit", "type": "integer"}, "coverAmount": {"description": "cover amount", "type": "string"}}}, "InsuranceCoverExclusionDetails": {"type": "object", "properties": {"exclusionExists": {"description": "Exclusion Exists", "type": "string"}}}, "InsuranceCoverLoadingDetails": {"type": "object", "properties": {"loadingUnit": {"description": "Loading Unit", "type": "string"}, "loadingAmount": {"description": "Loading Amount", "type": "string"}}}, "RequestedInsuranceCover": {"type": "object", "properties": {"occupationRating": {"description": "Occupation Rating", "type": "string"}, "deathInsuranceCover": {"$ref": "#/definitions/DeathInsuranceCover"}, "disabilityInsuranceCover": {"$ref": "#/definitions/DisabilityInsuranceCover"}, "incomeProtectionInsuranceCover": {"$ref": "#/definitions/IncomeProtectionInsuranceCover"}, "premium": {"type": "object", "properties": {"deathPremium": {"description": "death weekly premium amount", "type": "string"}, "tpdPremium": {"description": "tpd weekly premium amount", "type": "string"}, "ipPremium": {"description": "ip weekly premium amount", "type": "string"}, "deathAndTPDPremium": {"description": "Death and TPD combined weekly premium amount", "type": "string"}}}}}, "RequestedInvestmentOptionDetails": {"required": ["investmentAssetName", "investmentAssetPercentage"], "type": "object", "properties": {"investmentAssetName": {"description": "Investment Asset Code", "type": "integer", "example": 11}, "investmentAssetPercentage": {"description": "Investment Asset Percentage.", "type": "integer", "example": 42}}}, "InvestmentChoice": {"required": ["investmentStrategy", "investmentOptions"], "type": "object", "properties": {"investmentStrategy": {"description": "Investment Strategy", "enum": ["Current", "Future", "Rollover"], "type": "string"}, "requestedInvestmentOption": {"type": "array", "items": {"$ref": "#/definitions/RequestedInvestmentOptionDetails"}}}}, "UpdateInvestmentChoiceRequest": {"required": ["investmentStrategy", "investmentOptions"], "type": "object", "properties": {"investmentStrategy": {"description": "Investment Strategy", "type": "string"}, "requestedInvestmentOption": {"type": "array", "items": {"$ref": "#/definitions/InvestmentChoiceOptionDetails"}}}}, "InvestmentChoiceOptionDetails": {"required": ["assetCode", "assetPercentage"], "type": "object", "properties": {"assetCode": {"description": "Investment Asset Code", "type": "integer", "example": 11}, "assetPercentage": {"description": "Investment Asset Percentage.", "type": "integer", "example": 42}}}, "InvestmentChoiceResponse": {"type": "object", "properties": {"effectiveDate": {"format": "date", "type": "string"}}}, "Rollover": {"type": "object", "properties": {"fundABN": {"description": "Fund ABN", "type": "string"}, "fundUSI": {"description": "Fund USI", "type": "string"}, "fundMemberIdentifier": {"description": "Fund Member Identifier", "type": "string"}, "fundName": {"description": "Fund Name", "type": "string"}}}, "Insurance": {"type": "object", "properties": {"eligibility": {"$ref": "#/definitions/Eligibility"}, "requestedInsuranceCoverDetails": {"$ref": "#/definitions/RequestedInsuranceCover"}}}, "AccountDetails": {"type": "object", "properties": {"accountType": {"enum": ["Unknown", "Shell", "Personal", "ESA"], "type": "string"}, "productCode": {"description": "Product Code", "type": "string"}, "employerCode": {"description": "Employer Code", "type": "string"}}}, "PersonalAccountDetails": {"type": "object", "properties": {"personalAccountRelationship": {"description": "Personal Account Relationship", "type": "string"}, "insurance": {"$ref": "#/definitions/Insurance"}, "investmentChoice": {"$ref": "#/definitions/InvestmentChoice"}, "rollover": {"type": "array", "items": {"$ref": "#/definitions/Rollover"}}}}, "SearchAccountRequest": {"type": "object", "properties": {"emailAddress": {"description": "Email Address", "type": "string"}, "mobileNumber": {"description": "Mobile Number", "type": "string"}, "givenNames": {"description": "Given Names", "type": "string"}, "familyName": {"description": "Family Name", "type": "string"}, "tfn": {"description": "TFN", "type": "string"}, "dateOfBirth": {"format": "date-time", "description": "Date Of Birth", "type": "string"}}, "example": {"emailAddress": "string", "mobileNumber": "string", "givenNames": "string", "familyName": "string", "tfn": "string", "dateOfBirth": "string"}}, "SearchAccountResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/SearchAccountResponseItem"}}}}, "SearchAccountResponseItem": {"type": "object", "properties": {"memberNumber": {"description": "Member Number", "type": "string"}, "uniqueAccountID": {"description": "Unique Account ID", "type": "string"}, "accountTypeCode": {"description": "Account Type Code", "type": "string"}, "accountStatus": {"description": "Account Status", "type": "string"}, "accountTypeSummaryName": {"description": "Account Type Summary Name", "type": "string"}, "title": {"description": "Title", "type": "string"}, "givenNames": {"description": "Given Names", "type": "string"}, "familyName": {"description": "Family Name", "type": "string"}, "dateOfBirth": {"format": "date-time", "description": "Date Of Birth", "type": "string"}, "mobileNumber": {"description": "Mobile Number", "type": "string"}, "homeEmailAddress": {"description": "Home Email Address", "type": "string"}, "alternateEmailAddress": {"description": "Alternate <PERSON><PERSON> Address", "type": "string"}, "username": {"description": "Username", "type": "string"}, "matchDescription": {"description": "Match Description", "type": "string"}}}, "CreateAccountRequest": {"type": "object", "properties": {"referenceNumber": {"description": "Unique confirmation number created for the request", "required": ["referenceNumber", "accountDetails", "personalDetails", "residentialAddress"], "type": "string"}, "accountDetails": {"$ref": "#/definitions/AccountDetails"}, "personalDetails": {"$ref": "#/definitions/PersonalDetails"}, "residentialAddress": {"$ref": "#/definitions/ResidentialAddress"}, "postalAddress": {"$ref": "#/definitions/PostalAddress"}, "contactDetails": {"$ref": "#/definitions/ContactDetails"}, "rolloverDetails": {"type": "array", "items": {"$ref": "#/definitions/Rollover"}}, "personalAccountDetails": {"$ref": "#/definitions/PersonalAccountDetails"}}, "example": {"referenceNumber": "string", "accountDetails": {"accountType": "Unknown", "productCode": "string", "employerCode": "string"}, "personalDetails": {"title": "string", "firstName": "string", "surname": "string", "gender": "string", "sexAtBirth": "Male", "dateOfBirth": "string", "tfn": "string"}, "residentialAddress": {"addressLine1": "string", "addressLine2": "string", "suburb": "string", "state": "string", "postCode": "string", "country": "string"}, "postalAddress": {"addressLine1": "string", "addressLine2": "string", "suburb": "string", "state": "string", "postCode": "string", "country": "string"}, "contactDetails": {"email": "string", "mobile": "string"}, "rolloverDetails": [{"fundABN": "string", "fundUSI": "string", "fundMemberIdentifier": "string", "fundName": "string"}], "personalAccountDetails": {"personalAccountRelationship": "string", "insurance": {"eligibility": {"claimEntitlementEligibility": {"question": "string", "answer": "yes"}, "terminalIllnessEligibility": {"question": "string", "answer": "yes"}, "tpdEligibility": {"question": "string", "answer": "yes"}, "employmentEligibility": {"question": "string", "answer": "yes"}, "occupationEligibility": {"question": "string", "answer": "yes"}}, "requestedInsuranceCoverDetails": {"occupationRating": "string", "deathInsuranceCover": {"insuranceCoverage": {"numberOfUnit": 0, "coverAmount": "string"}, "isUnitised": true, "loadingDetails": {"loadingUnit": "string", "loadingAmount": "string"}, "exclusionDetails": {"exclusionExists": "string"}, "status": "string", "eligibilityReason": "Unknown"}, "disabilityInsuranceCover": {"insuranceCoverage": {"numberOfUnit": 0, "coverAmount": "string"}, "isUnitised": true, "loadingDetails": {"loadingUnit": "string", "loadingAmount": "string"}, "exclusionDetails": {"exclusionExists": "string"}, "status": "string", "eligibilityReason": "Unknown"}, "incomeProtectionInsuranceCover": {"status": "string", "eligibilityReason": "Unknown", "insuranceCoverage": {"numberOfUnit": 0, "coverAmount": "string"}, "isUnitised": true, "loadingDetails": {"loadingUnit": "string", "loadingAmount": "string"}, "exclusionDetails": {"exclusionExists": "string"}, "waitingPeriod": {"waitingDays": "string"}, "benefitDetails": {"benefitPeriod": "string"}}, "premium": {"deathPremium": "string", "tpdPremium": "string", "ipPremium": "string", "deathAndTPDPremium": "string"}}}, "investmentChoice": {"investmentStrategy": "Current", "requestedInvestmentOption": [{"investmentAssetName": 11, "investmentAssetPercentage": 42}]}, "rollover": [{"fundABN": "string", "fundUSI": "string", "fundMemberIdentifier": "string", "fundName": "string"}]}}}, "SearchSuperAccountRequest": {"type": "object", "properties": {"tfn": {"description": "TFN", "type": "string"}, "givenName": {"description": "First Name or Given Name", "type": "string"}, "surname": {"description": "Last Name or Surname", "type": "string"}, "dateOfBirth": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}, "email": {"type": "string"}}, "example": {"tfn": "string", "givenName": "string", "surname": "string", "dateOfBirth": "string", "email": "string"}}, "SearchSuperAccountResponse": {"type": "array", "items": {"$ref": "#/definitions/Account"}}, "Account": {"type": "object", "properties": {"memberNumber": {"type": "string"}, "productCode": {"type": "string"}, "productDescription": {"type": "string"}, "preTaxMemberContribution": {"type": "string"}, "postTaxMemberContribution": {"type": "string"}, "memberElectedContributionRateDesc": {"type": "string"}}}, "ErrorResponseItem": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "accountCreationStatus": {"type": "object", "properties": {"memberNumber": {"type": "string"}, "memberStatus": {"description": "Returning,New,Existing", "type": "string"}, "fund": {"description": "UNIS - Super,INDX -Indexed Pension, FLEX-Flexi Pension", "type": "string"}, "accountNumber": {"type": "string"}, "uniqueAccountId": {"type": "string"}, "requestStatus": {"description": "In Progress, Completed, Error", "type": "string"}, "requestedDateTime": {"format": "date-time", "type": "string"}, "completedDateTime": {"format": "date-time", "type": "string"}, "unisuperIdentifier": {"description": "Unisuper issued member identifier", "type": "string"}, "isMOLRegistered": {"description": "returns true if member is registered for member online", "type": "boolean"}, "username": {"description": "member online username", "type": "string"}}}, "BenefitNominationRequest": {"type": "object", "properties": {"effectiveDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "nominationType": {"type": "string", "example": "NonBinding"}, "beneficiaries": {"type": "array", "items": {"$ref": "#/definitions/Beneficiary"}}}, "example": {"effectiveDate": "string", "nominationType": "NonBinding", "beneficiaries": [{"percentage": 100, "relationship": "Spouse", "personTitle": "Mr", "givenName": "<PERSON>", "familyName": "<PERSON><PERSON>"}]}}, "BenefitNomination": {"type": "object", "properties": {"nominationType": {"type": "string", "example": "NonBinding"}, "beneficiaries": {"type": "array", "items": {"$ref": "#/definitions/BeneficiaryResponse"}}}}, "Beneficiary": {"type": "object", "properties": {"percentage": {"type": "number", "example": 100}, "relationship": {"type": "string", "example": "Spouse"}, "personTitle": {"type": "string", "example": "Mr"}, "givenName": {"type": "string", "example": "<PERSON>"}, "familyName": {"type": "string", "example": "<PERSON><PERSON>"}}}, "BeneficiaryResponse": {"properties": {"percentage": {"type": "number", "example": 100}, "relationship": {"type": "string", "example": "Spouse"}, "beneficiaryCategory": {"type": "string", "example": "Current"}, "effectiveDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "expiryDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "personalDetails": {"$ref": "#/definitions/BeneficiaryPersonalDetails"}, "emailDetails": {"properties": {"electronicMailUsageCode": {"type": "string", "example": "Unknown"}, "electronicMailAddress": {"type": "string", "example": "<EMAIL>"}}}}}, "BeneficiaryPersonalDetails": {"properties": {"personTitle": {"type": "string", "example": "Mr"}, "givenName": {"type": "string", "example": "<PERSON>"}, "familyName": {"type": "string", "example": "<PERSON><PERSON>"}, "gender": {"type": "string", "example": "Male"}, "sexAtBirth": {"$ref": "#/definitions/SexAtBirth"}, "dateOfBirth": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "address": {"$ref": "#/definitions/ResidentialAddress"}}}, "ThirdPartyAuthorityDetails": {"properties": {"documentIdentifier": {"type": "string"}, "documentSignedDate": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "dateRevoked": {"format": "date", "description": "Date format yyyy-MM-dd", "type": "string"}, "authorityDetails": {"type": "array", "items": {"$ref": "#/definitions/AuthorityDetailsType"}}}}, "AuthorityDetailsType": {"properties": {"relationshipToMember": {"type": "string"}, "authorisedThirdPartyName": {"type": "string"}, "companyName": {"type": "string"}, "authorisedRepresentativeNumber": {"type": "string"}}}, "RevokeThirdPartyAuthorityRequest": {"properties": {"supportingDocument": {"$ref": "#/definitions/DocumentDetailsType"}}, "example": {"supportingDocument": {"documentIdentifier": "1E1C6D79-1166-11EA-80F9-005056A32EE0", "documentIdentifierScheme": "PensionMemberNumber", "documentName": "Binding Death Nomination Application or", "documentUserCode": "F00023 0518", "documentSource": "KTM", "documentDescription": "string", "documentOwnerIdentifier": "abcd123456", "documentOwnerIdentifierScheme": "*********", "documentMimeType": "application/pdf", "documentUrlReference": "https://somestorageacc.blob.core.windows.net/documents/**********/0000035A.pdf"}}}, "DocumentDetailsType": {"required": ["documentIdentifier", "documentSource", "documentOwnerIdentifier", "documentOwnerIdentifierScheme", "documentMimeType", "documentURLReference"], "type": "object", "properties": {"documentIdentifier": {"description": "A unique identifier for the document", "type": "string", "example": "1E1C6D79-1166-11EA-80F9-005056A32EE0"}, "documentIdentifierScheme": {"description": "This field identifies the pertinent identifier scheme.", "type": "string", "example": "PensionMemberNumber"}, "documentName": {"description": "A meaningful name for the document.", "type": "string", "example": "Binding Death Nomination Application or"}, "documentUserCode": {"description": "The well known code for this document (\"MOL_CORRO\" for example).", "type": "string", "example": "F00023 0518"}, "documentSource": {"description": "The source system for this document.", "type": "string", "example": "KTM"}, "documentDescription": {"description": "A meaningful description for the document.", "type": "string"}, "documentOwnerIdentifier": {"description": "This field identifies the pertinent owner (member number for example).", "type": "string", "example": "abcd123456"}, "documentOwnerIdentifierScheme": {"description": "This field identifies the pertinent owner scheme. UniqueAccountID/MemberNumber(If ApplyToNewPensionAccount is true)", "type": "string", "example": "*********"}, "documentMimeType": {"description": "Genrally these values appear in request messages. text/html for email bodies and application/pdf for pdf attachments. Consumer is responsible to populate the right mime type for the attached document.", "type": "string", "example": "application/pdf"}, "documentUrlReference": {"description": "Document URL Reference should contain URL of the Azure storage account where the document is stored.", "type": "string", "example": "https://somestorageacc.blob.core.windows.net/documents/**********/0000035A.pdf"}}}, "SexAtBirth": {"description": "SexAtBirth", "enum": ["Male", "Female", "Indeterminate", "Unknown"], "type": "string"}, "EligibilityReason": {"description": "Eligibility Reason", "enum": ["Unknown", "Pending", "NotEligible", "MemberOptOut"], "type": "string"}, "EligibilityAnswer": {"description": "answer yes, no, NotEligible or MemberOptOut to determine claim entitlement eligibility", "enum": ["yes", "no", "NotEligible", "MemberOptOut"], "type": "string"}, "InsuranceDetails": {"properties": {"memberNumber": {"description": "Member number", "type": "integer"}, "deathInsuranceCoverUnits": {"description": "Death Insurance Cover Units", "type": "number"}, "deathInsuranceCoverAmount": {"description": "Death Insurance Cover Amount", "type": "number"}, "totalAndPermanentDisabilityInsuranceCoverUnits": {"description": "Total And Permanent Disability Insurance Cover Units", "type": "number"}, "totalAndPermanentDisabilityInsuranceCoverAmount": {"description": "Total And Permanent Disability Insurance Cover Amount", "type": "number"}, "incomeProtectionInsuranceCoverUnits": {"description": "Income Protection Insurance Cover Units", "type": "number"}, "incomeProtectionInsuranceCoverAmount": {"description": "Income Protection Insurance Cover Amount", "type": "number"}, "incomeProtectionWaitingPeriod": {"description": "Income Protection Waiting Period", "type": "string"}, "incomeProtectionBenefitPeriod": {"description": "Income Protection Benefit Period", "type": "string"}, "inbuiltTemporaryIncapacityBenefit": {"description": "Inbuilt Temporary Incapacity Benefit", "type": "number"}, "accumulationBalance": {"description": "Accumulation Balance", "type": "number"}, "definedBenefitBalance": {"description": "Defined Benefit Balance", "type": "number"}, "totalDefinedBenefitDeathBenefit": {"description": "Total Defined Benefit Death Benefit", "type": "number"}, "inbuiltDeathBenefit": {"description": "Inbuilt Death Benefit", "type": "number"}, "totalAccumulationDeathBenefit": {"description": "Total Accumulation Death Benefit", "type": "number"}, "totalTPDBenefit": {"description": "Total TPD Benefit", "type": "number"}, "isIncomeProtectionAutoUpdateOut": {"description": "Is Income Protection Auto Update Out?", "type": "boolean"}, "incomeProtectionAutoUpdateIneligibilityReason": {"description": "Income Protection Auto Update Ineligibility Reason", "type": "string"}, "insuranceEffectiveDate": {"format": "date-time", "description": "Insurance Effective Date", "type": "string"}, "isPending": {"description": "Is Pending?", "type": "boolean"}, "deathCoverEligibilityCode": {"description": "Death Cover Eligibility Code", "type": "string"}, "deathCoverEligibilityDescription": {"description": "Death Cover Eligibility Description", "type": "string"}, "incomeProtectionEligibilityCode": {"description": "Income Protection Eligibility Code", "type": "string"}, "incomeProtectionEligibilityDescription": {"description": "Income Protection Eligibility Description", "type": "string"}, "tpdCoverEligibilityCode": {"description": "TPD Cover Eligibility Code", "type": "string"}, "tpdCoverEligibilityDescription": {"description": "TPD Cover Eligibility Description", "type": "string"}, "accountBalanceEffectiveDate": {"format": "date-time", "description": "Account Balance Effective Date", "type": "string"}, "sftInsuranceCode": {"description": "SFT Insurance code - Eg- ACSF, UNIS (For unisuper insurance)", "type": "string", "example": "ACSF"}, "sftGrandFatheredCode": {"description": "SFTGrandFatheredCode", "type": "string", "example": "A: Z Fixed Death and TPD; B: Z Fixed Income Protection"}}}, "SubmitPensionApplicationRequest": {"required": ["memberNumber", "formTemplateName", "formTemplatePayload"], "type": "object", "properties": {"memberNumber": {"minimum": 1, "type": "integer"}, "isMemberDetailsCorrect": {"type": "boolean"}, "isPensionEligibilityDeclared": {"type": "boolean"}, "isTtrEligibilityDeclared": {"type": "boolean"}, "isTransferMoreThan25K": {"type": "boolean"}, "isCombineSuperRequired": {"type": "boolean"}, "isLumpSumContributionNotRequired": {"type": "boolean"}, "isNoLumpSumContLessThan4Days": {"type": "boolean"}, "isReceivedPensionIncomeDetails": {"type": "boolean"}, "isReceivedInstitutionDetails": {"type": "boolean"}, "isReceivedPaymentFrequencyDetails": {"type": "boolean"}, "isCorrectDrawDownDetails": {"type": "boolean"}, "isCorrectInvestmentChoiceDetails": {"type": "boolean"}, "isCorrectBeneficiaryNominationDetails": {"type": "boolean"}, "isPensionAppSignedAndDated": {"type": "boolean"}, "isPoiReceivedLessThan12Months": {"type": "boolean"}, "isTfnDecReceived": {"type": "boolean"}, "isPensionConsolidationNotRequired": {"type": "boolean"}, "isDbMemberEmployedNotTransferring": {"type": "boolean"}, "isAuthorisedIndicator": {"type": "boolean"}, "authorisedDate": {"format": "date", "type": "string"}, "formTemplateName": {"description": "Name of the template in CCMS", "type": "string"}, "formTemplatePayload": {"description": "Payload for the form template (as json string)", "type": "string"}}, "example": {"memberNumber": 0, "isMemberDetailsCorrect": true, "isPensionEligibilityDeclared": true, "isTtrEligibilityDeclared": true, "isTransferMoreThan25K": true, "isCombineSuperRequired": true, "isLumpSumContributionNotRequired": true, "isNoLumpSumContLessThan4Days": true, "isReceivedPensionIncomeDetails": true, "isReceivedInstitutionDetails": true, "isReceivedPaymentFrequencyDetails": true, "isCorrectDrawDownDetails": true, "isCorrectInvestmentChoiceDetails": true, "isCorrectBeneficiaryNominationDetails": true, "isPensionAppSignedAndDated": true, "isPoiReceivedLessThan12Months": true, "isTfnDecReceived": true, "isPensionConsolidationNotRequired": true, "isDbMemberEmployedNotTransferring": true, "isAuthorisedIndicator": true, "authorisedDate": "string", "formTemplateName": "string", "formTemplatePayload": "string"}}, "PensionBankDetails": {"properties": {"accountName": {"description": "Pension Bank account name", "type": "string"}, "accountNumber": {"description": "Pension Bank account number", "type": "string"}, "bsb": {"description": "BSB of account", "type": "string"}, "institutionName": {"description": "Financial institution name", "type": "string"}, "institutionBranchName": {"description": "Financial institution Branch Name", "type": "string"}, "percentageAllocated": {"format": "double", "description": "Percentage Alloted", "type": "number"}, "bankCode": {"description": "Bank Code", "type": "string"}, "lastModifiedDateTime": {"format": "date-time", "description": "Last modified date time", "type": "string"}}}, "CommunicationPreferenceDetails": {"required": ["communicationCategory", "communicationPreference"], "properties": {"communicationCategory": {"description": "Communication Category", "type": "string"}, "communicationPreference": {"enum": ["EMAIL_PERSONAL", "EMAIL_WORK", "POST_AND_EMAIL_PERSONAL", "POST_AND_EMAIL_WORK", "POST"], "type": "string"}, "effectiveDate": {"format": "date", "description": "Effective Date", "type": "string"}}, "example": {"communicationCategory": "string", "communicationPreference": "EMAIL_PERSONAL", "effectiveDate": "string"}}, "WithdrawPensionAmountRequest": {"required": ["formTemplateName", "formTemplatePayload"], "type": "object", "properties": {"drawdownMethod": {"type": "string", "example": "NoSelection"}, "withdrawalAmount": {"type": "number", "example": 10000}, "transitionToRetirementIndicator": {"type": "boolean", "example": false}, "authorisedIndicator": {"type": "boolean", "example": true}, "authorisedDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string", "example": "2021-07-03"}, "formTemplateName": {"description": "Name of the template in CCMS", "type": "string", "example": "PensionWithdrawal"}, "formTemplatePayload": {"description": "Payload for the form template (as json string)", "type": "string", "example": {"MemberNumber": "********", "UniqueAccountId": "1E1C6D79-1166-11EA-80F9-005056A32EE0", "MemberName": "NEIL GREENHILL", "DateSubmitted": "2021-03-07", "PreviousBalance": "374928.98", "AmountRequested": "10000.00", "EstimatedBalance": "364928.98", "FullAmount": false}}}, "example": {"drawdownMethod": "NoSelection", "withdrawalAmount": 10000, "transitionToRetirementIndicator": false, "authorisedIndicator": true, "authorisedDate": "2021-07-03", "formTemplateName": "PensionWithdrawal", "formTemplatePayload": {"MemberNumber": "********", "UniqueAccountId": "1E1C6D79-1166-11EA-80F9-005056A32EE0", "MemberName": "NEIL GREENHILL", "DateSubmitted": "2021-03-07", "PreviousBalance": "374928.98", "AmountRequested": "10000.00", "EstimatedBalance": "364928.98", "FullAmount": false}}}, "PensionExtendedDetails": {"properties": {"memberNumber": {"description": "Member Number", "type": "string"}, "dob": {"format": "date-time", "type": "string"}, "webRegistrationStatus": {"type": "string"}, "accountType": {"description": "Type of Pension account", "type": "string"}, "includeMembersForSuperPayments": {"description": "IncludeMembersForSuperPayments", "type": "string"}, "accountStatus": {"description": "Pension account account", "type": "string"}, "isPaymentSuspended": {"description": "Pension payment status", "type": "boolean"}}}, "DrawdownMethodDetails": {"type": "object", "properties": {"drawdownMethod": {"description": "drawdown method", "required": ["drawdown<PERSON><PERSON><PERSON>", "drawdownChoiceOptions"], "type": "string"}, "drawdownChoiceOptions": {"type": "array", "items": {"$ref": "#/definitions/DrawdownChoiceOptions"}}}, "example": {"drawdownMethod": "string", "drawdownChoiceOptions": [{"investmentAssetClass": "string", "investmentAssetName": "string", "investmentAssetCode": "string", "investmentDrawdownOrder": 0, "investmentAssetAllocation": "string"}]}}, "DrawdownChoiceOptions": {"properties": {"investmentAssetClass": {"description": "Asset Class", "type": "string"}, "investmentAssetName": {"description": "Asset Name", "type": "string"}, "investmentAssetCode": {"description": "Asset Code", "type": "string"}, "investmentDrawdownOrder": {"description": "Drawdown Order", "type": "integer"}, "investmentAssetAllocation": {"description": "Asset Allocation (optional field --> Added as part of Flexi pension withdrawal project. To populate asset allocation in PDF)", "type": "string"}}}, "AccountBalanceHistory": {"properties": {"effectiveDate": {"format": "date", "type": "string"}, "definedBenefitAmount": {"description": "Defined Benefit Amount", "type": "number"}, "accumulationAmount": {"description": "Accumulation Amount", "type": "number"}, "totalBalance": {"description": "Account total balance", "type": "number"}}}, "InvestmentProfileDetails": {"type": "object", "properties": {"investmentStrategy": {"type": "string", "example": "Current/Future/Rollover"}, "investmentStrategyEffectiveDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string"}, "investmentStrategyStatus": {"type": "string", "example": "Confirmed"}, "investmentOptionProfile": {"type": "array", "items": {"$ref": "#/definitions/InvestmentOptionProfile"}}}}, "InvestmentOptionProfile": {"type": "object", "properties": {"investmentAssetClass": {"type": "string", "example": "pre-mixed/self-select"}, "investmentAssetName": {"type": "string", "example": "Sustainable Balanced"}, "investmentAssetCode": {"type": "integer", "example": 14}, "investmentAssetPercentage": {"type": "number", "example": 100}, "investmentAssetBalanceAmount": {"type": "number", "example": 100}}}, "SwitchHistoryDetails": {"type": "object", "properties": {"requestedDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string"}, "effectiveDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string"}, "switchStrategy": {"type": "string", "example": "Current"}, "switchStatus": {"type": "string", "example": "Confirmed"}, "groupOffset": {"type": "integer", "example": 1}, "investmentOptionProfile": {"type": "array", "items": {"$ref": "#/definitions/InvestmentOptionProfile"}}}}, "AccountBalanceDetail": {"properties": {"accountTypeCode": {"description": "Account Type Code", "type": "string"}, "AccountDescription": {"description": "Account Description", "type": "string"}, "userRegistrationDate": {"format": "date-time", "description": "User Registration Date", "type": "string"}, "accountStatus": {"description": "Account status", "type": "string"}, "membershipType": {"description": "Membership Type", "type": "string"}, "sftMemberIdentifier": {"description": "Successor Fund Transfer (SFT) Member Number", "type": "string"}, "sftOriginatingFundCode": {"description": "Successor Fund Transfer (SFT) Originating Fund Code eg- \"AC\" for ACS ,  \"A\" for new members", "type": "string", "example": "AC"}, "sftAccountIdentifier": {"description": "Successor Fund Transfer (SFT) Account Number", "type": "string"}, "sftCommencementDate": {"format": "date", "description": "Successor Fund Transfer (SFT) commencement date", "type": "string"}, "accountBalance": {"$ref": "#/definitions/Balance"}}}, "Balance": {"properties": {"totalBalance": {"description": "Account total balance", "type": "number"}, "balanceEffectiveDate": {"format": "date-time", "description": "Balance Effective Date", "type": "string"}, "accountCommencementDate": {"format": "date-time", "description": "Account Commencement Date", "type": "string"}, "allowNonConcessionalContribution": {"description": "Allow Non Concessional Contribution", "type": "number"}, "definedBenefitAmount": {"description": "Defined Benefit Amount", "type": "number"}, "accumulationAmount": {"description": "Accumulation Amount", "type": "number"}, "preservedAmount": {"description": "Preserved Amount", "type": "number"}, "nonPreservedRestrictedAmount": {"description": "Non Preserved Restricted Amount", "type": "number"}, "nonPreservedUnrestrictedAmount": {"description": "Non Preserved Unrestricted Amount", "type": "number"}, "threeYearAverageBenefitSalary": {"description": "Three Year Average Benefit Salary", "type": "number"}, "fiveYearAverageBenefitSalary": {"description": "Five Year Average Benefit Salary", "type": "number"}, "lumpSumFactor": {"description": "Lump Sum Factor", "type": "number"}, "averageContributionFactor": {"description": "Average Contribution Factor", "type": "number"}, "pre2015ContributingService": {"description": "Pre 2015 Contributing Service", "type": "number"}, "post2015ContributingService": {"description": "Post 2015 Contributing Service", "type": "number"}, "averageServiceFractionFundService": {"description": "Average Service Fraction Fund Service", "type": "number"}, "preDecember2000ContributingServiceFundService": {"description": "Pre December 2000 Contributing Service Fund Service", "type": "number"}, "qualifyingBenefitServiceFundService": {"description": "Qualifying Benefit Service Fund Service", "type": "number"}, "relevantAverageServiceFractionFundService": {"description": "Relevant Average Service Fraction Fund Service", "type": "number"}, "pre2015ContributingServiceAndDisablementReduction": {"description": "Pre 2015 Contributing Service And Disablement Reduction", "type": "number"}, "post2015ContributingServiceAndDisablementReduction": {"description": "Post 2015 Contributing Service And Disablement Reduction", "type": "number"}, "qualifyingBenefitServiceReduction": {"description": "Qualifying Benefit Service Reduction", "type": "number"}, "preDecember2000ContributingServiceReduction": {"description": "Pre December 2000 Contributing Service Reduction", "type": "number"}, "additionalAccumulationContributionComponent": {"description": "Additional Accumulation Contribution Component", "type": "number"}, "additionalAccumulationContributionComponentReduction": {"description": "Additional Accumulation Contribution Component Reduction", "type": "number"}, "benefitServiceBefore1January2002": {"description": "Benefit Service Before 1 January2002", "type": "number"}, "isTransitionToRetirement": {"description": "IsTransitionToRetirement", "type": "boolean"}, "hasBenefitC34Participation": {"description": "HasBenefitC34Participation", "type": "boolean"}, "benefitC34StartDate": {"format": "date-time", "description": "Benefit C34 StartDate", "type": "string"}, "benefitYearsPostC34": {"description": "Benefit Years Post C34", "type": "number"}}}, "StatementBalanceHistory": {"properties": {"balanceEffectiveDate": {"format": "date-time", "type": "string"}, "definedBenefitAmount": {"description": "Defined Benefit Amount", "type": "number"}, "accumulationAmount": {"description": "Accumulation Amount", "type": "number"}, "totalBalance": {"description": "Account total balance", "type": "number"}}}, "PensionPaymentDetails": {"properties": {"pensionInstalments": {"type": "array", "items": {"$ref": "#/definitions/PensionInstalments"}}, "nextPensionPaymentDate": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}, "annualPension": {"description": "Annual Pension", "type": "number"}, "pensionStartDate": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}, "tapTerm": {"description": "TAP Term", "type": "number"}, "pensionPaymentPaidForFinancialYear": {"description": "Pension Payment Paid For FinancialYear", "type": "number"}, "pensionPaymentStartOfFinancialYearDate": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}, "pensionPaymentEndOfFinancialYearDate": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}, "annualTaxFreeComponent": {"description": "Annual Tax Free Component", "type": "number"}, "minimumPensionValue": {"description": "Minimum Pension Value", "type": "number"}, "maximumPensionValue": {"description": "Maximum Pension Value", "type": "number"}, "pensionPaymentStatus": {"description": "Pension Payment Status", "type": "string"}, "isPaymentSuspended": {"description": "Pension Payment Status", "type": "boolean"}, "processingDate": {"format": "date", "description": "dd/mm/yyyy", "type": "string"}}}, "RetainInsuranceCoverRequest": {"required": ["effectiveDate", "channelSource", "receiptDate"], "type": "object", "properties": {"effectiveDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string", "example": "2021-09-02"}, "channelSource": {"type": "string", "example": "F"}, "receiptDate": {"description": "date in local date time format (Australian Eastern, no timezone offset)", "type": "string", "example": "06/03/2023 11:31:32 AM"}}, "example": {"effectiveDate": "2021-09-02", "channelSource": "F", "receiptDate": "06/03/2023 11:31:32 AM"}}, "PensionInstalments": {"properties": {"paymentFrequency": {"description": "Payment Frequency", "type": "string"}, "instalmentPaymentAmount": {"description": "Installment Payment amount", "type": "number"}}}, "PaymentElections": {"properties": {"frequency": {"description": "eg F,M,Q,S for F= Fortnightly,M = Monthly,Q = Quarterly,S = Semi-Annually, A= Annually", "enum": ["Fortnightly", "Monthly", "Quarterly", "Semi-Annually", "Annually"], "type": "string"}, "pensionIndicator": {"description": "PensionIndicator , N,O,X (<PERSON><PERSON>,Nominated,Maximum)", "enum": ["Minimum", "Nominated", "Maximum"], "type": "string"}, "annualMonth": {"description": "AnnualMonth", "type": "string"}, "amount": {"format": "double", "description": "New amount per payment period , expected when pension indicator is Nominated", "type": "number"}}, "example": {"frequency": "Fortnightly", "pensionIndicator": "Minimum", "annualMonth": "string", "amount": 0}}, "CreateNoiRequest": {"required": ["originatingSystem", "financialYearDate", "claimAmount", "previousClaimAmount"], "type": "object", "properties": {"originatingSystem": {"enum": ["MOL", "KOFAX"], "type": "string"}, "financialYearDate": {"format": "date", "type": "string"}, "claimAmount": {"type": "number", "example": 509.55}, "previousClaimAmount": {"type": "number", "example": 888.77}}, "example": {"originatingSystem": "MOL", "financialYearDate": "string", "claimAmount": 509.55, "previousClaimAmount": 888.77}}, "NominateThirdPartyAuthorityRequest": {"properties": {"fundMemberIdentifier": {"type": "string", "example": "1256935"}, "identifierScheme": {"type": "string", "example": "MemberIdentifier"}, "uniqueAccountId": {"type": "string", "example": "32154"}, "senderEmail": {"format": "email", "type": "string", "example": "<EMAIL>"}, "requestorType": {"type": "string", "example": "FinancialAdvisor"}, "memberFullName": {"type": "string", "example": "<PERSON>"}, "nominateThirdPartyAuthorityDetails": {"$ref": "#/definitions/NominateThirdPartyAuthorityDetails"}}, "example": {"fundMemberIdentifier": "1256935", "identifierScheme": "MemberIdentifier", "uniqueAccountId": "32154", "senderEmail": "<EMAIL>", "requestorType": "FinancialAdvisor", "memberFullName": "<PERSON>", "nominateThirdPartyAuthorityDetails": {"formDetails": {"formIdentifier": "F00052 0520", "lodgedDate": "string", "authorisedIndicator": true, "authorisedDate": "string", "instructions": "string", "supportingDocument": {"documentIdentifier": "1E1C6D79-1166-11EA-80F9-005056A32EE0", "documentIdentifierScheme": "PensionMemberNumber", "documentName": "Binding Death Nomination Application or", "documentUserCode": "F00023 0518", "documentSource": "KTM", "documentDescription": "string", "documentOwnerIdentifier": "abcd123456", "documentOwnerIdentifierScheme": "*********", "documentMimeType": "application/pdf", "documentUrlReference": "https://somestorageacc.blob.core.windows.net/documents/**********/0000035A.pdf"}}, "authorityDetails": {"relationshipToMember": "financial advisor,attorney, accountant", "authorisedThirdPartyName": "DE<PERSON>", "companyName": "WEALTH Advisor", "authoriseAdviserStaffIndicator": true, "authorisedRepresentativeNumber": "1007101", "australianFinancialServicesLicenceNumber": "string"}}}}, "NominateThirdPartyAuthorityDetails": {"properties": {"formDetails": {"$ref": "#/definitions/FormDetailsWithDocument"}, "authorityDetails": {"$ref": "#/definitions/AuthorityDetails"}}}, "FormDetailsWithDocument": {"required": ["formIdentifier", "lodgedDate", "authorisedIndicator", "authorisedDate"], "type": "object", "properties": {"formIdentifier": {"description": "A unique identifier for the form.", "type": "string", "example": "F00052 0520"}, "lodgedDate": {"format": "date", "type": "string"}, "authorisedIndicator": {"type": "boolean"}, "authorisedDate": {"format": "date", "type": "string"}, "instructions": {"type": "string"}, "supportingDocument": {"$ref": "#/definitions/DocumentDetailsType"}}}, "AuthorityDetails": {"required": ["authorisedThirdPartyName"], "type": "object", "properties": {"relationshipToMember": {"type": "string", "example": "financial advisor,attorney, accountant"}, "authorisedThirdPartyName": {"type": "string", "example": "DE<PERSON>"}, "companyName": {"type": "string", "example": "WEALTH Advisor"}, "authoriseAdviserStaffIndicator": {"type": "boolean"}, "authorisedRepresentativeNumber": {"type": "string", "example": "1007101"}, "australianFinancialServicesLicenceNumber": {"type": "string"}}}, "NotionalTaxedContribution": {"type": "object", "properties": {"startDate": {"format": "date", "description": "Start date", "type": "string"}, "endDate": {"format": "date", "description": "End date", "type": "string"}, "newEntrantRate": {"description": "New Entrant Rate", "type": "number"}, "fullTimeEquivalentSalary": {"description": "Full Time Equivalent Salary.", "type": "number"}, "contributionDays": {"description": "Contribution Days", "type": "number"}, "definedBenefitAmount": {"description": "The Defined Benefit Amount", "type": "number"}, "ntcTotal": {"description": "NTC Total", "type": "number"}}}, "RolloverOutcomeCommunicationRequest": {"type": "object", "properties": {"sourceFundUSI": {"type": "string", "example": "17088268884001"}, "sourceFundMemberNumber": {"type": "string", "example": "SAAC011"}, "sourceFundName": {"type": "string", "example": "Australian Super"}, "rolloverStatus": {"type": "string", "example": "FAILUREOTHERFUND"}, "rolloverStatusDescription": {"type": "string", "example": "Member not found"}}, "example": {"sourceFundUSI": "17088268884001", "sourceFundMemberNumber": "SAAC011", "sourceFundName": "Australian Super", "rolloverStatus": "FAILUREOTHERFUND", "rolloverStatusDescription": "Member not found"}}, "InsuranceCoverDetail": {"type": "object", "properties": {"typeOfCover": {"description": "Insurance Cover type", "enum": ["TPD", "IP", "Death"]}, "eligibilityReason": {"format": "string", "description": "Eligibility reason", "type": "string", "example": "M"}}}, "CancelInsuranceCoverRequest": {"type": "object", "properties": {"effectiveDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string", "example": "2021-09-02"}, "memberGivenName": {"format": "string", "description": "Given name for member", "type": "string", "example": "<PERSON>"}, "memberEmailAddress": {"format": "string", "description": "Email address for member", "type": "string", "example": "<PERSON><PERSON>@company.com.au"}, "referenceId": {"format": "string", "description": "Reference ID", "type": "string", "example": "INS280469"}, "insuranceCoverDetails": {"items": {"$ref": "#/definitions/InsuranceCoverDetail"}}}, "example": {"effectiveDate": "2021-09-02", "memberGivenName": "<PERSON>", "memberEmailAddress": "<PERSON><PERSON>@company.com.au", "referenceId": "INS280469", "insuranceCoverDetails": {}}}, "SubmitNOIStatusRequest": {"required": ["effectiveDate", "status", "transactionRequestId"], "type": "object", "properties": {"effectiveDate": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string", "example": "2021-09-02"}, "status": {"enum": ["ACCEPTED", "REJECTED"], "type": "string", "example": "REJECTED"}, "transactionRequestId": {"type": "string", "example": "1234"}, "rejectedReason": {"enum": ["NOT_ENOUGH_CONTRIBUTION", "PARTIAL_BENEFIT", "FULL_BENEFIT", "PENSION_INCOME", "NO_CONTRIBUTIONS_PAID", "TIMEFRAME_ELAPSED", "SPOUSE_CONTRIBUTION", "NO_ORIGINAL_NOI", "ALREADY_CLAIMED", "CONTS_PAID_TO_DBD_AND_NOT_ENOUGH_TO_ACCUM"], "type": "string", "example": "ALREADY_CLAIMED"}}, "example": {"effectiveDate": "2021-09-02", "status": "REJECTED", "transactionRequestId": "1234", "rejectedReason": "ALREADY_CLAIMED"}}, "NoticeOfIntentClaimStatusResponse": {"type": "object", "properties": {"referenceNmber": {"type": "string", "example": "11288"}, "accountNumber": {"type": "string", "example": "F14815"}, "requestStatus": {"enum": ["Completed", "Disallowed", "In progress", "Superseded"], "type": "string"}, "requestDate": {"format": "date", "type": "string"}, "effectiveDate": {"format": "date", "type": "string"}, "financialYear": {"format": "date", "type": "string"}, "presentClaimAmount": {"format": "currency", "type": "number", "example": 6619.82}, "previousClaimAmount": {"format": "currency", "type": "number", "example": 2.45}, "claimAmount": {"format": "currency", "type": "number", "example": 2.25}, "variance": {"format": "int", "type": "number"}, "totalClaimAmount": {"format": "currency", "type": "number", "example": 6624.52}, "documentName": {"type": "string", "example": "AD_UNIS_M_F14815_20210421_A.PDF"}}}, "MemberContributionFlexibilityDetails": {"type": "object", "properties": {"title": {"type": "string", "example": "MR"}, "familyName": {"type": "string", "example": "RANKIN"}, "givenName": {"type": "string", "example": "JAMES PETER HARRY"}, "otherGivenNames": {"type": "string"}, "birthDate": {"format": "date", "type": "string"}, "dateJoinedFund": {"format": "date", "type": "string"}, "contFlexEffectiveDate": {"format": "date", "type": "string"}, "ContFlexEmployerRatePercentage": {"type": "number", "example": 17}, "contFlexMemberRate": {"type": "string", "example": "0.00% Post-Tax / 0.00% Pre-Tax"}}}, "UnappliedTransactionDetails": {"properties": {"accountBalanceDate": {"format": "date", "type": "string"}, "investmentStrategyID": {"description": "Investment Strategy ID in ODS", "type": "integer", "example": 12}, "investmentStrategyName": {"description": "Investment strategy chosen by the member in MOL.", "type": "string", "example": "International Shares"}, "totalInterestToDate": {"format": "currency", "type": "number", "example": 5802.45}, "interestFromDate": {"format": "date", "type": "string", "example": "2022-07-01"}, "previousFYInterestToDate": {"format": "currency", "type": "number", "example": 1234.56}, "currentFYInterestToDate": {"format": "currency", "type": "number", "example": 4567.89}}}, "SubmitSuperWithdrawalRequest": {"required": ["withdrawalType", "withdrawalAmount", "estimatedAccountBalance", "conditionOfRelease", "bankAccountDetails", "personalDetailsSnapshot", "withdrawalDistributionType", "summaryDeclarationPage", "citizenshipDeclaration", "invalidNotSuppliedTfnDeclaration", "estimatedWithdrawAmount"], "type": "object", "properties": {"withdrawalType": {"enum": ["Partial", "Full"], "type": "string"}, "withdrawalAmount": {"format": "currency", "type": "number", "example": 100000}, "estimatedAccountBalance": {"format": "currency", "type": "number", "example": 1000000}, "summaryDeclarationPage": {"type": "boolean", "example": true}, "citizenshipDeclaration": {"type": "boolean", "example": true}, "invalidNotSuppliedTfnDeclaration": {"type": "boolean", "example": true}, "withdrawalDistributionType": {"enum": ["Net", "Gross"], "type": "string"}, "paymentType": {"type": "string", "example": "Nominated Amount"}, "paymentTypeCode": {"type": "string", "example": "NominatedAmount"}, "estimatedWithdrawAmount": {"format": "currency", "type": "number", "example": 100000}, "conditionOfRelease": {"type": "string", "example": "Ceased Current Employment"}, "memberAccountType": {"type": "string", "example": "DBD, Accum1"}, "terminatedEmploymentDetails": {"type": "array", "items": {"$ref": "#/definitions/TerminatedEmploymentDetails"}}, "bankAccountDetails": {"$ref": "#/definitions/BankAccountDetails"}, "personalDetailsSnapshot": {"$ref": "#/definitions/PersonalDetailsSnapshot"}}, "example": {"withdrawalType": "Partial", "withdrawalAmount": 100000, "estimatedAccountBalance": 1000000, "summaryDeclarationPage": true, "citizenshipDeclaration": true, "invalidNotSuppliedTfnDeclaration": true, "withdrawalDistributionType": "Net", "paymentType": "Nominated Amount", "paymentTypeCode": "NominatedAmount", "estimatedWithdrawAmount": 100000, "conditionOfRelease": "Ceased Current Employment", "memberAccountType": "DBD, Accum1", "terminatedEmploymentDetails": [{"employerName": "ACME Corporation", "employmentEndDate": "string", "employerNumber": "232"}], "bankAccountDetails": {"bankAccountName": "Mr. <PERSON>", "bsb": "123-456", "bankAccountNumber": "*********", "institutionName": "Commonwealth Bank"}, "personalDetailsSnapshot": {"name": "<PERSON>", "address": "123 Main Street, Melbourne VIC 3000", "dateOfBirth": "string", "personalEmailAddress": "<EMAIL>", "mobileNumber": "**********", "preservationAge": 40, "taxFileNumberStatus": "SuppliedValid"}}}, "TerminatedEmploymentDetails": {"type": "object", "properties": {"employerName": {"type": "string", "example": "ACME Corporation"}, "employmentEndDate": {"format": "date", "type": "string"}, "employerNumber": {"type": "string", "example": "232"}}}, "BankAccountDetails": {"required": ["bankAccountName", "bsb", "bankAccountNumber", "institutionName"], "type": "object", "properties": {"bankAccountName": {"type": "string", "example": "Mr. <PERSON>"}, "bsb": {"type": "string", "example": "123-456"}, "bankAccountNumber": {"type": "string", "example": "*********"}, "institutionName": {"type": "string", "example": "Commonwealth Bank"}}}, "PersonalDetailsSnapshot": {"description": "An object to capture personal details used for the point-in-time PDF generation.", "required": ["name", "address", "dateOfBirth", "personalEmail<PERSON><PERSON><PERSON>", "mobileNumber", "taxFileNumberStatus", "preservationAge"], "type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "string", "example": "123 Main Street, Melbourne VIC 3000"}, "dateOfBirth": {"format": "date", "type": "string"}, "personalEmailAddress": {"type": "string", "example": "<EMAIL>"}, "mobileNumber": {"type": "string", "example": "**********"}, "preservationAge": {"type": "integer", "example": 40}, "taxFileNumberStatus": {"enum": ["SuppliedValid", "SuppliedInvalid", "NotSupplied"], "type": "string"}}}, "SuperBankAccountDetail": {"properties": {"accountName": {"description": "Super Bank account name", "type": "string"}, "accountNumber": {"description": "Super Bank account number", "type": "string"}, "bsb": {"description": "BSB of account", "type": "string"}, "institutionName": {"description": "Financial institution name", "type": "string"}}}, "CentrelinkScheduleRequest": {"type": "object", "properties": {"requesterType": {"description": "Requester type", "enum": ["Member", "Other", "MOL"], "type": "string"}, "requesterName": {"description": "Name of the requester", "type": "string"}, "correspondenceType": {"description": "Digicomm send type", "enum": ["Email", "Post"], "type": "string"}, "email": {"format": "email", "description": "email address to receive the centrelink schedule", "type": "string"}}, "example": {"requesterType": "Member", "requesterName": "string", "correspondenceType": "Email", "email": "string"}}, "FlexiPensionWithdrawalRequest": {"type": "object", "properties": {"drawdownMethodDetails": {"$ref": "#/definitions/DrawdownMethodDetails"}, "dateSubmitted": {"format": "date", "description": "date in ISO8601 format (YYYY-MM-DD)", "type": "string", "example": "2021-07-03"}, "withdrawalType": {"enum": ["Partial", "Full"], "type": "string"}, "withdrawalAmount": {"format": "currency", "type": "number", "example": 100000}, "estimatedAccountBalance": {"format": "currency", "type": "number", "example": 1000000}, "isInternationalBankAccount": {"type": "boolean", "example": false}, "bankAccountDetails": {"$ref": "#/definitions/BankAccountDetails"}, "personalDetailsSnapshot": {"$ref": "#/definitions/PersonalDetailsSnapshot"}}, "example": {"drawdownMethodDetails": {"drawdownMethod": "string", "drawdownChoiceOptions": [{"investmentAssetClass": "string", "investmentAssetName": "string", "investmentAssetCode": "string", "investmentDrawdownOrder": 0, "investmentAssetAllocation": "string"}]}, "dateSubmitted": "2021-07-03", "withdrawalType": "Partial", "withdrawalAmount": 100000, "estimatedAccountBalance": 1000000, "isInternationalBankAccount": false, "bankAccountDetails": {"bankAccountName": "Mr. <PERSON>", "bsb": "123-456", "bankAccountNumber": "*********", "institutionName": "Commonwealth Bank"}, "personalDetailsSnapshot": {"name": "<PERSON>", "address": "123 Main Street, Melbourne VIC 3000", "dateOfBirth": "string", "personalEmailAddress": "<EMAIL>", "mobileNumber": "**********", "preservationAge": 40, "taxFileNumberStatus": "SuppliedValid"}}}, "FlexiPensionPersonalDetailsSnapshot": {"description": "An object to capture personal details used for the point-in-time PDF generation.", "required": ["name", "address", "dateOfBirth", "personalEmail<PERSON><PERSON><PERSON>", "mobileNumber"], "type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "string", "example": "123 Main Street, Melbourne VIC 3000"}, "dateOfBirth": {"format": "date", "type": "string"}, "personalEmailAddress": {"type": "string", "example": "<EMAIL>"}, "mobileNumber": {"type": "string", "example": "**********"}}}}, "tags": [{"name": "accounts", "description": "Operations for retrieving and updating member account data."}, {"name": "communicationPreferences", "description": "Operations for managing communication preferences"}]}