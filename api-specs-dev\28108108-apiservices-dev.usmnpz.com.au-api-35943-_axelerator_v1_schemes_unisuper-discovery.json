{"basePath": "/axelerator/v1/schemes/unisuper", "host": "apiservices-dev.usmnpz.com.au", "info": {"title": "Swagger Specification 2.0. Host: apiservices-dev.usmnpz.com.au", "version": "2.0"}, "paths": {"/members/{param1}/amended_insurance": {"post": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/details": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/insurance": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/search/members": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}}, "schemes": ["http"], "swagger": "2.0"}