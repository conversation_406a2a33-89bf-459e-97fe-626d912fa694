# main.tf

module "imperva_policy" {
  source = "../../modules/imperva-policy"
  imperva_api_id = var.imperva_api_id
  imperva_api_key = var.imperva_api_key
  imperva_account_id = var.imperva_account_id
  api_security_sites = var.api_security_sites
  api_security_api_configs = var.api_security_api_configs
  depends_on = [module.provider_config]
}

module "provider_config" {
  source = "../../globals"
}

output "policy_status" {
  value = "Dev environment policy applied"
}

