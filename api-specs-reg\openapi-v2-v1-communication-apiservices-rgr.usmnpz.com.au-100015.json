{"swagger": "2.0", "info": {"title": "Communication Service API", "version": "1.0", "description": "API for rendering and transmitting outbound communication (email/SMS) to members and employers."}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1/communication", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/email/send": {"post": {"description": "Sends an email to the specified address.", "operationId": "sendEmail", "summary": "Sends an email to the specified address.", "tags": ["communication"], "parameters": [{"name": "context", "in": "query", "description": "Indicates whether email would be sent synchronously or asynchronously. (value to be set as sync/async) - default is async", "type": "string", "default": "async", "enum": ["async", "sync"]}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "emailRequestPayload", "in": "body", "schema": {"$ref": "#/definitions/EmailRequestPayload"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/sms/send": {"post": {"description": "Sends an SMS text message to the specified telephone number.", "operationId": "sendSms", "summary": "Sends an SMS text message to the specified telephone number.", "tags": ["communication"], "parameters": [{"name": "context", "in": "query", "description": "Indicates whether email would be sent synchronously or asynchronously. (value to be set as sync/async) - default is async", "type": "string", "default": "async", "enum": ["async", "sync"]}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "smsRequestPayload", "in": "body", "schema": {"$ref": "#/definitions/SmsRequestPayload"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"204": {"description": "Request succeeded, no response body"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}}, "definitions": {"EmailBodyTypeEnumeration": {"enum": ["html", "text"], "type": "string"}, "EmailRequestPayload": {"type": "object", "properties": {"toAddress": {"description": "The email address of the intended recipient.", "maxLength": 254, "minLength": 1, "type": "string"}, "carbonCopyAddress": {"description": "The email address of an interested party.", "maxLength": 254, "minLength": 1, "type": "string"}, "fromAddress": {"description": "The email address of the sender (e.g. <EMAIL>). Optional attribute, to be used when Email needs to be sent synchronously. Must be one of pre-approved emails from distribution list.", "maxLength": 254, "minLength": 1, "type": "string"}, "emailBodyType": {"$ref": "#/definitions/EmailBodyTypeEnumeration"}, "storeCorrespondence": {"description": "A flag to indicate if the correspondence should be stored.", "type": "boolean"}, "template": {"$ref": "#/definitions/TemplateDetail"}, "documentMetadata": {"$ref": "#/definitions/DocumentMetadata"}, "attachments": {"$ref": "#/definitions/Attachments"}, "attachmentUrls": {"description": "Attachment Urls.", "type": "array", "items": {"type": "string"}}}, "example": {"toAddress": "string", "carbonCopyAddress": "string", "fromAddress": "string", "emailBodyType": "html", "storeCorrespondence": true, "template": {"templateName": "string", "templateParameters": [{"name": "string", "value": "string"}]}, "documentMetadata": {"documentIdentifier": "string", "documentName": "string", "documentUserCode": "string", "documentSource": "string", "documentDescription": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string"}, "attachments": [{"name": "string", "contentType": "string", "content": "string"}], "attachmentUrls": ["string"]}}, "SmsRequestPayload": {"type": "object", "properties": {"telephoneNumber": {"description": "The telephone number of the intended recipient.", "minLength": 1, "type": "string"}, "storeCorrespondence": {"description": "A flag to indicate if the correspondence should be stored.", "type": "boolean"}, "template": {"$ref": "#/definitions/TemplateDetail"}, "documentMetadata": {"$ref": "#/definitions/DocumentMetadata"}}, "example": {"telephoneNumber": "string", "storeCorrespondence": true, "template": {"templateName": "string", "templateParameters": [{"name": "string", "value": "string"}]}, "documentMetadata": {"documentIdentifier": "string", "documentName": "string", "documentUserCode": "string", "documentSource": "string", "documentDescription": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string"}}}, "NameValuePair": {"type": "object", "properties": {"name": {"description": "The name of the template parameter.", "minLength": 1, "type": "string"}, "value": {"description": "The value to be substituted for this template parameter.", "minLength": 1, "type": "string"}}}, "TemplateDetail": {"description": "Template details to render document.", "type": "object", "properties": {"templateName": {"description": "The friendly name of the content template.", "minLength": 1, "type": "string"}, "templateParameters": {"description": "A collection of name value pairs that map to content template parameters.", "type": "array", "items": {"$ref": "#/definitions/NameValuePair"}}}}, "DocumentMetadata": {"description": "Metadata that should be added to the rendered document.", "type": "object", "properties": {"documentIdentifier": {"description": "A unique identifier for the document.", "type": "string"}, "documentName": {"description": "A meaningful name for the document.", "type": "string"}, "documentUserCode": {"description": "The well known code for this document (\"MOL_CORRO\" for example).", "type": "string"}, "documentSource": {"description": "The source system for this document.", "type": "string"}, "documentDescription": {"description": "A meaningful description for the document.", "type": "string"}, "documentOwnerIdentifier": {"description": "This field identifies the pertinent owner (member number for example).", "type": "string"}, "documentOwnerIdentifierScheme": {"description": "This field identifies the pertinent owner scheme (\"SuperMemberNumber\" for example).", "type": "string"}}}, "Attachment": {"description": "An attachment to be included in the email. Not supported when email needs to be sent synchronously.", "required": ["name", "contentType", "content"], "type": "object", "properties": {"name": {"description": "Name of the attachment.", "type": "string"}, "contentType": {"description": "The content type of the attachment.", "type": "string"}, "content": {"format": "byte", "description": "The content of the attachment encoded as base64 string.", "type": "string"}}}, "Attachments": {"description": "A collection of attachments to be included in the email. Not supported when email needs to be sent synchronously.", "type": "array", "items": {"$ref": "#/definitions/Attachment"}}, "ErrorResponseItem": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}}, "tags": [{"name": "communication", "description": "Operations for sending and storing outbound communications."}]}