{"swagger": "2.0", "info": {"title": "Document Service API", "version": "1.0", "description": "API for storing and retrieving documents from Objective document store."}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1/document", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/insert": {"post": {"description": "The response will have a document Id if the value of X-ProcessType is Sync in the header. If it is Async or null, the response will not provide a document Id. Default behaviour is async.", "operationId": "insertDocument", "summary": "Stores document in Objective document store.", "tags": ["insert"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-OriginatingSystem", "in": "header", "description": "Format - string. Name of the system originating the request.", "required": true, "type": "string", "format": "string"}, {"name": "X-ProcessType", "in": "header", "description": "Format - string. Possible values are Async or Sync. Default is Async if there is no value provided.", "type": "string", "format": "string"}, {"name": "insertDocumentRequest", "in": "body", "schema": {"$ref": "#/definitions/InsertDocumentRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "The service call has completed successfully (in case of sync request)", "schema": {"$ref": "#/definitions/StoreDocumentResponse"}, "examples": {"application/json": {"documentIdentifier": "string"}}}, "204": {"description": "Request succeeded, no content (in case of async request)"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{documentId}": {"get": {"description": "This endpoint can retrieve a document from Objective document store.", "operationId": "retrieveDocument", "summary": "Retrieves a document from Objective document store.", "tags": ["retrieve"], "parameters": [{"name": "documentId", "in": "path", "description": "Unique Document Identifier", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "The service call has completed successfully", "schema": {"$ref": "#/definitions/RetrieveDocumentResponse"}, "examples": {"application/json": {"documentContent": "string", "documentName": "string", "documentType": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "The user has not authenticated with the server"}, "403": {"description": "The document id provided is blank"}, "404": {"description": "The document does not exist or the user does not have the privileges to open the document."}, "410": {"description": "The document identified by the id has been deleted."}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/upload": {"post": {"description": "This endpoint will allow uploading of document such as pdf, docx, doc, jpg, jpeg and png.", "operationId": "uploadDocument", "summary": "Allows to upload document.", "tags": ["upload"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string"}, {"name": "X-BusinessTransactionId", "in": "header", "description": "Client-generated GUID with no decoration such as curly braces or any string value.", "type": "string"}, {"name": "document", "in": "formData", "type": "string"}, {"name": "metadata", "in": "formData", "type": "string"}], "consumes": ["multipart/form-data"], "produces": ["application/json"], "responses": {"202": {"description": "The service call has completed successfully", "schema": {"$ref": "#/definitions/UploadDocumentResponse"}, "examples": {"application/json": {"documentReference": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "The user has not authenticated with the server"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "413": {"description": "The request entity is larger than limits defined by server"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/render": {"post": {"description": "Generates a pdf document and returns it", "operationId": "renderDocument", "summary": "Generates a pdf document and returns it", "tags": ["Render a document"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "renderDocumentRequest", "in": "body", "schema": {"$ref": "#/definitions/RenderDocumentRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded, document result returned", "schema": {"$ref": "#/definitions/RenderDocumentResponse"}, "examples": {"application/json": {"documentMimeType": "string", "documentContent": {"documentContentSource": "Unknown", "document": "string"}}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/statements/search": {"post": {"description": "Operation for searching a document based on filters in the statementDB.", "operationId": "SearchStatements", "summary": "Operation for searching a document based on filters in the statementDB.", "tags": ["search"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "searchStatementsRequest", "in": "body", "schema": {"$ref": "#/definitions/SearchStatementsRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded, Search result returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/SearchStatementsResponse"}}, "examples": {"application/json": [{"documentIdentifier": "string", "documentIdentifierScheme": "string", "documentCategory": "string", "documentType": "string", "documentStatus": "string", "documentSize": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string", "createdDate": "string", "statementDate": "string", "documentVideoStatement": "string"}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "503": {"description": "The request cannot be served - DB is offline"}}}}, "/statements/{statementId}": {"get": {"description": "This service can retrieve statement document from DB", "operationId": "GetStatement", "summary": "Retrieves Statement document from StatementsDB", "tags": ["retrieve"], "parameters": [{"name": "statementId", "in": "path", "description": "Format - int64. Unique Document Identifier in statements DB", "required": true, "type": "integer", "format": "int64"}, {"name": "statementDate", "in": "query", "description": "Format - date (as full-date in RFC3339).", "required": true, "type": "string", "format": "date"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-Identifier", "in": "header", "description": "This is the identifier used to search in the statementsDB", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "The service call has completed successfully", "schema": {"$ref": "#/definitions/RetrieveStatementResponse"}, "examples": {"application/json": {"documentContent": "string", "documentMimeType": "string", "documentType": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "The user has not authenticated with the server"}, "403": {"description": "The statement id provided is blank"}, "404": {"description": "The statement document does not exist or the user does not have the privileges to open the document."}, "410": {"description": "The statement document identified by the id has been deleted."}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseItem"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/correspondence-register/{document-name}": {"get": {"description": "Retrieves document from Acurity Correspondence Register Database.", "operationId": "RetrieveCorrespondenceRegisterDocument", "summary": "Retrieves document from Acurity Correspondence Register Database.", "tags": ["retrieve"], "parameters": [{"name": "document-name", "in": "path", "description": "document name", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/CorrespondenceRegisterDocumentResponse"}, "examples": {"application/json": {"documentContent": "string"}}}, "204": {"description": "No Document Found"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{memberAliasId}/retrieve-correspondence-list": {"get": {"description": "retrieve the correspondence list for the account.", "operationId": "RetrieveCorrespondenceList", "summary": "Retrieve Correspondence List.", "tags": ["retrieve"], "parameters": [{"name": "memberAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg.'9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"type": "array", "items": {"$ref": "#/definitions/Correspondences"}}, "examples": {"application/json": [{"uniqueAccountId": "string", "onlineDocumentUniqueId": "string", "documentType": "string", "createdDate": "string", "newFileName": "string", "fileSize": 100}]}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/{memberAliasId}/retrieve-correspondence/{documentUniqueId}": {"get": {"description": "retrieve the correspondence document.", "operationId": "RetrieveCorrespondence", "summary": "Retrieve Correspondence.", "tags": ["retrieve"], "parameters": [{"name": "memberAliasId", "in": "path", "description": "UniSuper issued member identifier (alias)", "required": true, "type": "string"}, {"name": "documentUniqueId", "in": "path", "description": "correspondence document uniqueid", "required": true, "type": "string"}, {"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded", "schema": {"$ref": "#/definitions/CorrespondenceDocument"}, "examples": {"application/json": {"fileName": "string", "documentContent": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "404": {"description": "Identifier not found"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/handle-correspondence-notifications": {"post": {"description": "Correspondence Notifications Handler.", "operationId": "HandleCorrespondenceNotifications", "summary": "Correspondence Notifications Handler.", "tags": ["Correspondence Notifications Handler"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg.'9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded"}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "404": {"description": "file not found / file invalid.", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}, "/member-correspondence": {"post": {"description": "Stores member correspondences in Objective document store, also update the link to this document in Unify(Practify).The response would be HTTP status code 202 once the request is successfully accepted for further processing.", "operationId": "memberCorrespondence", "summary": "Insert Member Correspondence", "tags": ["member correspondence"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}, {"name": "X-OriginatingSystem", "in": "header", "description": "Format - string. Name of the system originating the request.", "required": true, "type": "string", "format": "string"}, {"name": "memberCorrespondenceRequest", "in": "body", "schema": {"$ref": "#/definitions/MemberCorrespondenceRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"202": {"description": "Request has been accepted for processing, but the processing has not been finished yet."}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}}}}}, "definitions": {"InsertDocumentRequest": {"type": "object", "properties": {"documentIdentifier": {"description": "A unique identifier for the document.", "type": "string"}, "documentName": {"description": "A meaningful name for the document.", "type": "string"}, "documentUserCode": {"description": "The well known code for this document (\"MOL_CORRO\" for example).", "type": "string"}, "documentSource": {"description": "The source system for this document.", "type": "string"}, "documentDescription": {"description": "A meaningful description for the document.", "type": "string"}, "documentOwnerIdentifier": {"description": "This field identifies the pertinent owner (member number for example).", "type": "string"}, "documentOwnerIdentifierScheme": {"description": "This field identifies the pertinent owner scheme (\"SuperMemberNumber\" for example).", "type": "string"}, "documentMimeType": {"description": "Generally these values appear in request messages. text/html for email bodies and application/pdf for pdf attachments. Consumer is responsible to populate the right mime type for the attached document.", "type": "string"}, "documentReferenceNumber": {"description": "This field identifies an external reference number for the document.", "type": "string"}, "documentContent": {"$ref": "#/definitions/documentContent"}}, "example": {"documentIdentifier": "string", "documentName": "string", "documentUserCode": "string", "documentSource": "string", "documentDescription": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string", "documentMimeType": "string", "documentReferenceNumber": "string", "documentContent": {"documentContentSource": "Unknown", "document": "string"}}}, "MemberCorrespondenceRequest": {"type": "object", "properties": {"documentIdentifier": {"description": "A unique identifier for the document.", "type": "string"}, "documentName": {"description": "A meaningful name for the document.", "type": "string"}, "documentUserCode": {"description": "The well known code for this document (\"MOL_CORRO\" for example).", "type": "string"}, "documentSource": {"description": "The source system for this document.", "type": "string"}, "documentDescription": {"description": "A meaningful description for the document.", "type": "string"}, "documentOwnerIdentifier": {"description": "This field identifies the pertinent owner (member number for example).", "type": "string"}, "documentOwnerIdentifierScheme": {"description": "This field identifies the pertinent owner scheme (\"SuperMemberNumber\" for example).", "type": "string"}, "documentMimeType": {"description": "Generally these values appear in request messages. text/html for email bodies and application/pdf for pdf attachments. Consumer is responsible to populate the right mime type for the attached document.", "type": "string"}, "documentContent": {"$ref": "#/definitions/documentContent"}}, "example": {"documentIdentifier": "string", "documentName": "string", "documentUserCode": "string", "documentSource": "string", "documentDescription": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string", "documentMimeType": "string", "documentContent": {"documentContentSource": "Unknown", "document": "string"}}}, "documentContent": {"type": "object", "properties": {"documentContentSource": {"$ref": "#/definitions/documentContentSourceEnumeration"}, "document": {"description": "Objective expects the corresponding node names to be mapped based on the value of the documentContentSource. Objective treats the field values case sensitive. document node will hold all expected document types.", "type": "string"}}}, "documentContentSourceEnumeration": {"description": "Complex type used to specify the content source and content of a document.Depending on the content source the specific document data field is filled up with the document's base64 encoded data or the document's reference path (URI of document).", "enum": ["Unknown", "DocumentData", "DocumentReference", "DocumentData64Binary"], "type": "string"}, "StoreDocumentResponse": {"type": "object", "properties": {"documentIdentifier": {"type": "string"}}}, "RetrieveDocumentResponse": {"type": "object", "properties": {"documentContent": {"type": "string"}, "documentName": {"type": "string"}, "documentType": {"type": "string"}}}, "SearchStatementsRequest": {"required": ["documentOwnerIdentifier"], "type": "object", "properties": {"documentOwnerIdentifier": {"description": "This field identifies the pertinent owner (member number for example).", "type": "string"}, "createdDateFrom": {"format": "date", "type": "string"}, "createdDateTo": {"format": "date", "type": "string"}, "maxNumberDocuments": {"type": "number"}}, "example": {"documentOwnerIdentifier": "string", "createdDateFrom": "string", "createdDateTo": "string", "maxNumberDocuments": 0}}, "SearchStatementsResponse": {"type": "object", "properties": {"documentIdentifier": {"description": "The statementID returned from the DB", "type": "string"}, "documentIdentifierScheme": {"description": "Identifier like Statement", "type": "string"}, "documentCategory": {"description": "Document Category", "type": "string"}, "documentType": {"description": "Document Type", "type": "string"}, "documentStatus": {"description": "Document Status", "type": "string"}, "documentSize": {"description": "Document Size", "type": "string"}, "documentOwnerIdentifier": {"description": "Document Owner Identifier", "type": "string"}, "documentOwnerIdentifierScheme": {"description": "Document Owner Identifier Scheme", "type": "string"}, "createdDate": {"format": "date", "type": "string"}, "statementDate": {"format": "date", "type": "string"}, "documentVideoStatement": {"description": "Video statement reference", "type": "string"}}}, "ErrorResponseItem": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}, "UploadDocumentResponse": {"type": "object", "properties": {"documentReference": {"description": "Reference location for a uploaded document.", "type": "string"}}}, "RenderDocumentRequest": {"required": ["template"], "type": "object", "properties": {"template": {"$ref": "#/definitions/TemplateDetail"}, "documentMetadata": {"$ref": "#/definitions/DocumentMetadata"}}, "example": {"template": {"templateName": "string", "templateParameters": [{"name": "string", "value": "string"}], "templatePayload": "string"}, "documentMetadata": {"documentIdentifier": "string", "documentType": "string", "documentMimeType": "string", "documentName": "string", "documentDescription": "string", "documentStatus": "string", "documentSource": "string", "documentOwnerIdentifier": "string", "documentOwnerIdentifierScheme": "string", "documentUserCode": "string"}}}, "RenderDocumentResponse": {"type": "object", "properties": {"documentMimeType": {"description": "Generally these values appear in request messages. text/html for email bodies and application/pdf for pdf attachments. Consumer is responsible to populate the right mime type for the attached document.", "type": "string"}, "documentContent": {"$ref": "#/definitions/documentContent"}}}, "DocumentMetadata": {"type": "object", "properties": {"documentIdentifier": {"description": "A unique identifier for the document.", "type": "string"}, "documentType": {"description": "document type like 'BenefitStatement'", "type": "string"}, "documentMimeType": {"description": "MimeType of the document eg:html/txt/pdf", "type": "string"}, "documentName": {"description": "Name of the document", "type": "string"}, "documentDescription": {"description": "Short description of the document", "type": "string"}, "documentStatus": {"description": "Status like Available", "type": "string"}, "documentSource": {"description": "Source like MOL/MSP", "type": "string"}, "documentOwnerIdentifier": {"description": "document owner identifier", "type": "string"}, "documentOwnerIdentifierScheme": {"description": "document owner identifier scheme", "type": "string"}, "documentUserCode": {"description": "The well known code for this document (\"MOL_CORRO\" for example).", "type": "string"}}}, "TemplateDetail": {"required": ["templateName"], "type": "object", "properties": {"templateName": {"description": "The friendly name of the content template.", "minLength": 1, "type": "string"}, "templateParameters": {"description": "A collection of name value pairs that map to content template parameters.", "type": "array", "items": {"$ref": "#/definitions/NameValuePair"}}, "templatePayload": {"description": "This is the json payload that will be sent to the CCMS", "minLength": 0, "type": "string"}}}, "NameValuePair": {"type": "object", "properties": {"name": {"description": "The name of the template parameter.", "minLength": 1, "type": "string"}, "value": {"description": "The value to be substituted for this template parameter.", "minLength": 1, "type": "string"}}}, "RetrieveStatementResponse": {"type": "object", "properties": {"documentContent": {"type": "string"}, "documentMimeType": {"description": "This is the mimeType of the document and can be text/pdf", "type": "string"}, "documentType": {"description": "Document type (Benefit Statement / Account Summary / Retirement income estimate)", "type": "string"}}}, "CorrespondenceRegisterDocumentResponse": {"type": "object", "properties": {"documentContent": {"type": "string"}}}, "Correspondences": {"properties": {"uniqueAccountId": {"description": "Account Id", "type": "string"}, "onlineDocumentUniqueId": {"format": "UUID", "description": "Document Id", "type": "string"}, "documentType": {"description": "Document type", "type": "string"}, "createdDate": {"format": "date", "description": "Document created at.", "type": "string"}, "newFileName": {"description": "Document new file name.", "type": "string"}, "fileSize": {"description": "Document file size.", "type": "integer", "example": 100}}}, "CorrespondenceDocument": {"properties": {"fileName": {"description": "Document file name.", "type": "string"}, "documentContent": {"description": "Document data - base64.", "type": "string"}}}}, "tags": [{"name": "document", "description": "Operations for storing documents in Objective document store."}]}