{"schemes": ["http"], "swagger": "2.0", "info": {"title": "Swagger Specification 2.0. Host: apiservices-rgr.usmnpz.com.au", "version": "2.0"}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/ping", "paths": {"/": {"get": {"parameters": [{"type": "string", "name": "X-Ms-User-Agent", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/2993331802_body"}}}}}}, "definitions": {"2993331802_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/ping,METHOD:GET,PARAMETER:http-rsp-body . 2993331802 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["status", "region", "environment"], "properties": {"environment": {"type": "string"}, "region": {"type": "string"}, "status": {"type": "string"}}}}}