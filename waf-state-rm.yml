trigger:
- none

variables:
  - group: terraform-cloud-team-token

# == STEP 1: DEFINE RUNTIME PARAMETERS ==
# This block creates a dropdown menu when you manually run the pipeline.
parameters:
- name: environment
  displayName: Environment to Target
  type: string
  default: dev
  values:
  - dev
  - regression
  - prod

stages:
- stage: Plan
  dependsOn: []
  jobs:
  - job: terraform_plan
    displayName: 'Terraform Plan for ${{ parameters.environment }}'
    workspace:
        clean: all
    pool:
        # vmImage: 'ubuntu-latest'
      name: infra-az-linux-default-npd 
    steps:
    - checkout: self
      path: self
      fetchDepth: 0
      clean: true
    - script: |
        echo "Working dir: $(pwd)"
        ls -la
      displayName: "Debug Info"
    - script: |                        
        # The TFE_TOKEN environment variable is already available here.
        # Terraform will use it automatically for init, plan, etc.
        terraform init -no-color
      displayName: 'Terraform Init'
      # workingDirectory: $(System.DefaultWorkingDirectory)/environments/
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
    - script: | 
        terraform validate -no-color
      displayName: 'Terraform Validate'
      # workingDirectory: $(System.DefaultWorkingDirectory)/environments/
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
    - script: |
        echo "Select workspace dev"
        terraform workspace select imperva-apiservices-dev
        echo "Remove the rgr site-level policy from the dev state"
        terraform state rm 'incapsula_api_security_api_config.api_config["apiservices_rgr_accounts"]'
        terraform state rm 'incapsula_api_security_api_config.api_config["apiservices_rgr_acurity_notifications"]'
        terraform state rm 'incapsula_api_security_api_config.api_config["apiservices_rgr_calculator"]'
        terraform state rm 'incapsula_api_security_api_config.api_config["apiservices_rgr_communication"]'
        terraform state rm 'incapsula_api_security_api_config.api_config["apiservices_rgr_document"]'
      displayName: "Terraform state remove regression from dev workspace"
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
