trigger:
- none

variables:
  - group: terraform-cloud-team-token

# == STEP 1: DEFINE RUNTIME PARAMETERS ==
# This block creates a dropdown menu when you manually run the pipeline.
parameters:
- name: environment
  displayName: Environment to Target
  type: string
  default: dev
  values:
  - dev
  - regression
  - prod

stages:
- stage: Plan
  dependsOn: []
  jobs:
  - job: terraform_plan
    displayName: 'Terraform Plan for ${{ parameters.environment }}'
    workspace:
        clean: all
    pool:
        # vmImage: 'ubuntu-latest'
      name: infra-az-linux-default-npd
    steps:
    - checkout: self
      path: self
      fetchDepth: 0
      clean: true
    - script: |
        echo "--- adding building tag ---"
        echo "##vso[build.addbuildtag]imperva-apiservices-$(environment)"       
      displayName: 'TAG: Adding a tag for this build'
    - script: |                        
        # The TFE_TOKEN environment variable is already available here.
        # Terraform will use it automatically for init, plan, etc.
        terraform init -no-color -upgrade
      displayName: 'Terraform Init'
      # workingDirectory: $(System.DefaultWorkingDirectory)/environments/
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
    - script: | 
        terraform validate -no-color
      displayName: 'Terraform Validate'
      # workingDirectory: $(System.DefaultWorkingDirectory)/environments/
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
    - script: |
         terraform plan -no-color -var-file=$(System.DefaultWorkingDirectory)/environments/${{ parameters.environment }}/${{ parameters.environment }}.tfvars
      displayName: 'Terraform Plan'
      env:
        TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
        TFC_TOKEN: $(TF_TOKEN_app_terraform_io)
        TF_WORKSPACE: imperva-apiservices-${{ parameters.environment }}
      # workingDirectory: $(System.DefaultWorkingDirectory)/environments/

# STAGE 2: Applies the plan after manual approval in Azure DevOps
# - stage: Apply
#   displayName: 'Apply Terraform Changes for ${{ parameters.environment }}'
#   dependsOn: Plan
#   jobs:
#   # deployment job that targets our environment with the approval gate
#   - deployment: terraform_apply
#     displayName: 'Run Terraform Apply'
#     pool:
#       vmImage: 'ubuntu-latest'
#     environment: 'waf-automation-${{ parameters.environment }}-apply'
#     variables:
#       TFE_TOKEN: $(TF_TOKEN_app_terraform_io)
#     strategy:
#       runOnce:
#         deploy:
#           steps:
#           - script: |
#               sudo apt-get update && sudo apt-get install -y wget unzip
#               wget https://releases.hashicorp.com/terraform/1.5.7/terraform_1.5.7_linux_amd64.zip
#               unzip terraform_1.5.7_linux_amd64.zip
#               sudo mv terraform /usr/local/bin/
#             displayName: "Install Terraform"

#           - checkout: self

#           - script: terraform init -no-color
#             displayName: 'Terraform Init'

#           # This command tells TFC to apply the plan that is currently pending approval.
#           # -auto-approve is required for non-interactive pipeline runs.
#           - script: terraform apply -parallelism=1 -auto-approve -no-color -var-file=$(System.DefaultWorkingDirectory)/environments/${{ parameters.environment }}/${{ parameters.environment }}.tfvars
#             displayName: 'Terraform Apply'
