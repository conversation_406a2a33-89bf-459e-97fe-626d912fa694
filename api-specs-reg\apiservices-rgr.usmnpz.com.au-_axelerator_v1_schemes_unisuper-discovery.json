{"schemes": ["http"], "swagger": "2.0", "info": {"title": "Swagger Specification 2.0. Host: apiservices-rgr.usmnpz.com.au", "version": "2.0"}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/axelerator/v1/schemes/unisuper", "paths": {"/members/{param1}/amended_insurance": {"post": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Parent-Id", "in": "header", "required": true}, {"type": "integer", "format": "int8", "name": "X-Datadog-Sampling-Priority", "in": "header", "required": true}, {"type": "string", "name": "X-Datadog-Tags", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Trace-Id", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/3405287639_body"}}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/insurance": {"get": {"parameters": [{"type": "string", "name": "param1", "in": "path", "required": true}, {"type": "string", "name": "effective_date", "in": "query"}, {"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Parent-Id", "in": "header", "required": true}, {"type": "integer", "format": "int8", "name": "X-Datadog-Sampling-Priority", "in": "header", "required": true}, {"type": "string", "name": "X-Datadog-Tags", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Trace-Id", "in": "header", "required": true}], "responses": {"200": {"description": " ", "schema": {"$ref": "#/definitions/3969012075_body"}}}}}, "/search/members": {"post": {"parameters": [{"type": "string", "name": "X-Correlationid", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Parent-Id", "in": "header", "required": true}, {"type": "integer", "format": "int8", "name": "X-Datadog-Sampling-Priority", "in": "header", "required": true}, {"type": "string", "name": "X-Datadog-Tags", "in": "header", "required": true}, {"type": "integer", "format": "int64", "name": "X-Datadog-Trace-Id", "in": "header", "required": true}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/1899952067_body"}}], "responses": {"200": {"description": "Array Elements are: object", "schema": {"type": "array", "items": {"$ref": "#/definitions/48903039_body"}}}}}}}, "definitions": {"1088049065_contactPerson": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>*->employmentHistory->contactPerson . 1088049065 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["email", "title", "givenNames", "homePhoneNumber", "workPhoneNumber", "surname", "mobilePhoneNumber", "faxNumber"], "properties": {"email": {"type": "string"}, "faxNumber": {}, "givenNames": {"type": "string"}, "homePhoneNumber": {"type": "string"}, "mobilePhoneNumber": {}, "surname": {"type": "string"}, "title": {"type": "string"}, "workPhoneNumber": {"type": "string"}}}, "1543298162_memberInformation": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInformation . 1543298162 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["addresses", "staffMember", "communicationPreferences", "email", "memberNumber", "faxNumber", "givenNames", "dateJoinedFund", "memberDivisionReference", "memberUUID", "workPhoneNumber", "restrictedAccessMMI", "superProductReference", "dateLeftFund", "homePhoneNumber", "occupationCategory", "memberInformationAmendments", "superBalance", "date<PERSON><PERSON><PERSON><PERSON><PERSON>", "blockedAccess", "dateOfBirth", "restrictedAccessClaims", "title", "salaryExcludingSuperannuation", "surname", "isAustralianResident", "gender", "integratedPartnerSystemId", "salarySuperannuationContributionPercentage", "mobilePhoneNumber"], "properties": {"addresses": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2127737576_addresses"}}, "blockedAccess": {"type": "boolean"}, "communicationPreferences": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "dateJoinedFund": {"type": "string"}, "dateLeftFund": {}, "dateOfBirth": {"type": "string"}, "dateOfDeath": {}, "email": {"type": "string"}, "faxNumber": {}, "gender": {"type": "string"}, "givenNames": {"type": "string"}, "homePhoneNumber": {"type": "string"}, "integratedPartnerSystemId": {"type": "string"}, "isAustralianResident": {"type": "string"}, "memberDivisionReference": {"type": "string"}, "memberInformationAmendments": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "memberNumber": {"type": "string"}, "memberUUID": {"type": "string"}, "mobilePhoneNumber": {"type": "string"}, "occupationCategory": {}, "restrictedAccessClaims": {"type": "boolean"}, "restrictedAccessMMI": {"type": "boolean"}, "salaryExcludingSuperannuation": {"type": "number", "format": "float64"}, "salarySuperannuationContributionPercentage": {"type": "number", "format": "float64"}, "staffMember": {"type": "boolean"}, "superBalance": {"type": "number", "format": "float64"}, "superProductReference": {"type": "string"}, "surname": {"type": "string"}, "title": {"type": "string"}, "workPhoneNumber": {"type": "string"}}}, "1899952067_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-req-body . 1899952067 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["givenNames", "integratedPartnerSystemId", "memberNumber", "dateOfBirth", "searchType", "surname"], "properties": {"dateOfBirth": {}, "givenNames": {}, "integratedPartnerSystemId": {}, "memberNumber": {}, "searchType": {"type": "string"}, "surname": {}}}, "2127737576_addresses": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInformation->addresses->>* . 2127737576 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["countryCode", "postCode", "state", "suburb", "addressLine1", "addressStatus", "addressType", "addressLine2"], "properties": {"addressLine1": {"type": "string"}, "addressLine2": {}, "addressStatus": {"type": "string"}, "addressType": {"type": "string"}, "countryCode": {"type": "string"}, "postCode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "2333800597_applicationProperties": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->applicationProperties->>* . 2333800597 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "245124930_coverWordings": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->plans->>*->covers->>*->coverWordings->>* . 245124930 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"category": {}, "code": {}, "description": {"type": "string"}, "insurerSystemId": {}, "integratedPartnerSystemId": {"type": "string"}, "name": {"type": "string"}}}, "2574704009_coverPremiumAdjustments": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->plans->>*->covers->>*->coverPremiumAdjustments->>* . 2574704009 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["name", "description", "percentValue", "dollarValue", "category", "code", "integratedPartnerSystemId", "insurerSystemId"], "properties": {"category": {}, "code": {}, "description": {}, "dollarValue": {}, "insurerSystemId": {}, "integratedPartnerSystemId": {"type": "string"}, "name": {"type": "string"}, "percentValue": {"type": "number", "format": "float64"}}}, "2818664626_covers": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->plans->>*->covers->>* . 2818664626 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["sumInsuredUnit", "coverStatus", "coverPremiumAdjustments", "annualPayableAmount", "applicationCoverChangeType", "occupationCategory", "benefitPeriod", "applicationCoverOutcome", "insurerSystemParentCoverId", "coverLastReinstated", "memberDivisionReference", "integratedPartnerSystemLinkedCoverId", "coverEndDate", "insurerSystemCoverId", "sumInsuredInDollars", "insurerSystemLinkedCoverId", "integratedPartnerSystemCoverId", "coverRiskCategory", "coverType", "<PERSON><PERSON><PERSON><PERSON>", "applicationCoverDecisionRationale", "coverStartDate", "coverWordings", "coverHasNotBeenContinuous", "integratedPartnerSystemParentCoverId", "annualPremiumAmount", "sumInsuredAmount", "superProductReference"], "properties": {"annualPayableAmount": {"type": "number", "format": "float64"}, "annualPremiumAmount": {"type": "number", "format": "float64"}, "applicationCoverChangeType": {}, "applicationCoverDecisionRationale": {}, "applicationCoverOutcome": {}, "benefitPeriod": {"type": "string"}, "coverEndDate": {}, "coverHasNotBeenContinuous": {}, "coverLastReinstated": {}, "coverPremiumAdjustments": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2574704009_coverPremiumAdjustments"}}, "coverRiskCategory": {"type": "string"}, "coverStartDate": {"type": "string"}, "coverStatus": {"type": "string"}, "coverType": {"type": "string"}, "coverWordings": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/245124930_coverWordings"}}, "insurerSystemCoverId": {}, "insurerSystemLinkedCoverId": {}, "insurerSystemParentCoverId": {}, "integratedPartnerSystemCoverId": {"type": "string"}, "integratedPartnerSystemLinkedCoverId": {}, "integratedPartnerSystemParentCoverId": {}, "memberDivisionReference": {"type": "string"}, "occupationCategory": {"type": "string"}, "sumInsuredAmount": {"type": "number", "format": "float64"}, "sumInsuredInDollars": {"type": "number", "format": "float64"}, "sumInsuredUnit": {"type": "string"}, "superProductReference": {"type": "string"}, "waitingPeriod": {}}}, "2874741629_coverPremiumAdjustments": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInsurance->plans->>*->covers->>*->coverPremiumAdjustments->>* . 2874741629 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "properties": {"category": {}, "code": {}, "description": {"type": "string"}, "dollarValue": {}, "insurerSystemId": {"type": "string"}, "integratedPartnerSystemId": {}, "name": {"type": "string"}, "percentValue": {"type": "number", "format": "float64"}}}, "290060175_covers": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInsurance->plans->>*->covers->>* . 290060175 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["coverEndDate", "benefitPeriod", "occupationCategory", "sumInsuredInDollars", "coverStartDate", "memberDivisionReference", "coverHasNotBeenContinuous", "integratedPartnerSystemLinkedCoverId", "annualPremiumAmount", "coverType", "insurerSystemParentCoverId", "coverRiskCategory", "insurerSystemLinkedCoverId", "<PERSON><PERSON><PERSON><PERSON>", "annualPayableAmount", "superProductReference", "coverStatus", "applicationCoverOutcome", "integratedPartnerSystemParentCoverId", "sumInsuredAmount", "insurerSystemCoverId", "coverLastReinstated", "applicationCoverChangeType", "integratedPartnerSystemCoverId", "sumInsuredUnit"], "properties": {"annualPayableAmount": {"type": "number", "format": "float64"}, "annualPremiumAmount": {"type": "number", "format": "float64"}, "applicationCoverChangeType": {"type": "string"}, "applicationCoverOutcome": {"type": "string"}, "benefitPeriod": {}, "coverEndDate": {}, "coverHasNotBeenContinuous": {"type": "boolean"}, "coverLastReinstated": {}, "coverPremiumAdjustments": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2874741629_coverPremiumAdjustments"}}, "coverRiskCategory": {}, "coverStartDate": {"type": "string"}, "coverStatus": {"type": "string"}, "coverType": {"type": "string"}, "insurerSystemCoverId": {"type": "string"}, "insurerSystemLinkedCoverId": {}, "insurerSystemParentCoverId": {}, "integratedPartnerSystemCoverId": {"type": "string"}, "integratedPartnerSystemLinkedCoverId": {}, "integratedPartnerSystemParentCoverId": {}, "memberDivisionReference": {}, "occupationCategory": {}, "sumInsuredAmount": {"type": "number", "format": "float64"}, "sumInsuredInDollars": {"type": "number", "format": "float64"}, "sumInsuredUnit": {"type": "string"}, "superProductReference": {}, "waitingPeriod": {}}}, "3116288321_business": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>*->employmentHistory->business . 3116288321 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["phoneNumber", "mobilePhoneNumber", "abn", "name", "faxNumber", "email", "acn"], "properties": {"abn": {"type": "string"}, "acn": {}, "email": {"type": "string"}, "faxNumber": {}, "mobilePhoneNumber": {}, "name": {"type": "string"}, "phoneNumber": {"type": "string"}}}, "3160977300_addresses": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>*->addresses->>* . 3160977300 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["addressLine2", "postCode", "state", "addressLine1", "addressStatus", "addressType", "countryCode", "suburb"], "properties": {"addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "addressStatus": {"type": "string"}, "addressType": {"type": "string"}, "countryCode": {"type": "string"}, "postCode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "3231194178_plans": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/insurance,METHOD:GET,PARAMETER:http-rsp-body->plans->>* . 3231194178 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["groupPolicyNumber", "integratedPartnerSystemId", "policyLastPaymentDate", "policyPaymentStatus", "policyStatus", "effectiveDate", "policyStartDate", "insurerSystemId", "covers"], "properties": {"covers": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2818664626_covers"}}, "effectiveDate": {"type": "string"}, "groupPolicyNumber": {"type": "string"}, "insurerSystemId": {}, "integratedPartnerSystemId": {"type": "string"}, "policyLastPaymentDate": {"type": "string"}, "policyPaymentStatus": {"type": "string"}, "policyStartDate": {"type": "string"}, "policyStatus": {"type": "string"}}}, "3398716088_addresses": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>*->employmentHistory->addresses->>* . 3398716088 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["state", "suburb", "addressLine2", "addressLine1", "addressStatus", "addressType", "countryCode", "postCode"], "properties": {"addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "addressStatus": {}, "addressType": {"type": "string"}, "countryCode": {"type": "string"}, "postCode": {"type": "string"}, "state": {"type": "string"}, "suburb": {"type": "string"}}}, "3405287639_body": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body . 3405287639 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["applicationType", "applicationNumber", "applicationSubmissionDate", "memberInsurance", "memberInformation", "applicationOutcome"], "properties": {"applicationNumber": {"type": "string"}, "applicationOutcome": {"type": "string"}, "applicationProperties": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/2333800597_applicationProperties"}}, "applicationSubmissionDate": {"type": "string"}, "applicationType": {"type": "string"}, "memberInformation": {"$ref": "#/definitions/1543298162_memberInformation"}, "memberInsurance": {"$ref": "#/definitions/441155743_memberInsurance"}}}, "3613124626_employmentHistory": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>*->employmentHistory . 3613124626 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["<PERSON><PERSON><PERSON>", "business", "hoursPerWeek", "dateOfEnd", "dateOfStart", "employmentStatus", "addresses"], "properties": {"addresses": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3398716088_addresses"}}, "business": {"$ref": "#/definitions/3116288321_business"}, "contactPerson": {"$ref": "#/definitions/1088049065_contactPerson"}, "dateOfEnd": {"type": "string"}, "dateOfStart": {"type": "string"}, "employmentStatus": {"type": "string"}, "hoursPerWeek": {}}}, "3701176556_plans": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInsurance->plans->>* . 3701176556 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["policyStartDate", "covers", "effectiveDate", "policyLastPaymentDate", "groupPolicyNumber", "policyStatus", "insurerSystemId", "policyPaymentStatus", "integratedPartnerSystemId"], "properties": {"covers": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/290060175_covers"}}, "effectiveDate": {"type": "string"}, "groupPolicyNumber": {"type": "string"}, "insurerSystemId": {"type": "string"}, "integratedPartnerSystemId": {"type": "string"}, "policyLastPaymentDate": {"type": "string"}, "policyPaymentStatus": {"type": "string"}, "policyStartDate": {"type": "string"}, "policyStatus": {"type": "string"}}}, "3969012075_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/insurance,METHOD:GET,PARAMETER:http-rsp-body . 3969012075 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["plans"], "properties": {"plans": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3231194178_plans"}}}}, "441155743_memberInsurance": {"description": "Definition for CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/members/*/amended_insurance,METHOD:POST,PARAMETER:http-req-body->memberInsurance . 441155743 : Hash of CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["plans"], "properties": {"plans": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3701176556_plans"}}}}, "48903039_body": {"description": "Definition for RESPONSE CODE:200,CONTENTTYPE:application/json,ENDPOINT:/axelerator/v1/schemes/unisuper/search/members,METHOD:POST,PARAMETER:http-rsp-body->>* . 48903039 : Hash of RESPONSECODE_CONTENTTYPE_METHOD_ENDPOINT_PARAMETER", "type": "object", "required": ["mobilePhoneNumber", "blockedAccess", "faxNumber", "isAustralianResident", "surname", "email", "integratedPartnerSystemId", "salaryExcludingSuperannuation", "gender", "workPhoneNumber", "dateLeftFund", "superBalance", "date<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateOfBirth", "memberNumber", "memberInformationAmendments", "restrictedAccessMMI", "staffMember", "givenNames", "restrictedAccessClaims", "superProductReference", "employmentHistory", "dateJoinedFund", "salarySuperannuationContributionPercentage", "title", "addresses", "homePhoneNumber", "occupationCategory", "memberUUID", "communicationPreferences", "memberDivisionReference"], "properties": {"addresses": {"description": "Array Elements are: object", "type": "array", "items": {"$ref": "#/definitions/3160977300_addresses"}}, "blockedAccess": {"type": "boolean"}, "communicationPreferences": {"description": "Array Elements are: string", "type": "array", "items": {"type": "string"}}, "dateJoinedFund": {"type": "string"}, "dateLeftFund": {"type": "string"}, "dateOfBirth": {"type": "string"}, "dateOfDeath": {"type": "string"}, "email": {"type": "string"}, "employmentHistory": {"$ref": "#/definitions/3613124626_employmentHistory"}, "faxNumber": {}, "gender": {"type": "string"}, "givenNames": {"type": "string"}, "homePhoneNumber": {"type": "string"}, "integratedPartnerSystemId": {"type": "string"}, "isAustralianResident": {"type": "string"}, "memberDivisionReference": {"type": "string"}, "memberInformationAmendments": {}, "memberNumber": {"type": "string"}, "memberUUID": {"type": "string"}, "mobilePhoneNumber": {"type": "string"}, "occupationCategory": {"type": "string"}, "restrictedAccessClaims": {"type": "boolean"}, "restrictedAccessMMI": {"type": "boolean"}, "salaryExcludingSuperannuation": {"type": "number", "format": "float64"}, "salarySuperannuationContributionPercentage": {"type": "number", "format": "float64"}, "staffMember": {"type": "boolean"}, "superBalance": {"type": "number", "format": "float64"}, "superProductReference": {"type": "string"}, "surname": {"type": "string"}, "title": {"type": "string"}, "workPhoneNumber": {"type": "string"}}}}}