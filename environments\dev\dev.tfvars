# environments/dev/dev.tfvars

api_security_sites = {
  "28108108" = {
    domain                           = "apiservices-dev.usmnpz.com.au" 
    is_api_only_site = false
    non_api_request_violation_action = "ALERT_ONLY"

    invalid_url_violation_action     = "ALERT_ONLY"
    invalid_method_violation_action  = "ALERT_ONLY"
    missing_param_violation_action   = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
    is_automatic_discovery_api_integration_enabled = true
    # invalid_param_name_violation_action = "ALERT_ONLY" # Not supported by provider
  }
}

api_security_api_configs = {
  "apiservices_dev_v1" = {
    site_id                           = "28108108"
    spec_file_path                     = "${path.module}/../../api-specs-dev/28108108-apiservices-dev.usmnpz.com.au-api-35896-_v1-discovery.json"
    description                        = "/v1"
    base_path                          = "/v1"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
  "apiservices_dev_axelerator" = {
    site_id                           = "28108108"
    spec_file_path                     = "${path.module}/../../api-specs-dev/28108108-apiservices-dev.usmnpz.com.au-api-35943-_axelerator_v1_schemes_unisuper-discovery.json"
    description                        = "/axelerator/v1/schemes/unisuper"
    base_path                          = "/axelerator/v1/schemes/unisuper"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
  "apiservices_dev_ping" = {
    site_id                           = "28108108"
    spec_file_path                     = "${path.module}/../../api-specs-dev/28108108-apiservices-dev.usmnpz.com.au-api-79662-_ping-discovery.json"
    description                        = "/ping"
    base_path                          = "/ping"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  }
}