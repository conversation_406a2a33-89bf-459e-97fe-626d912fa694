{"swagger": "2.0", "info": {"title": "Acurity Notifications API", "version": "1.0", "description": "An API for handling member events emitted by Acurity."}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1/acurity-notifications", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/send": {"post": {"description": "Publishes a new notification event.", "operationId": "send", "summary": "Publishes a new notification event.", "tags": ["notifications"], "parameters": [{"name": "employerContactDetailsIncomplete", "in": "body", "schema": {"$ref": "#/definitions/EmployerContactDetailsIncomplete"}, "description": "This is a sample request only. The API can receive various XML payloads."}], "consumes": ["application/xml"], "produces": ["text/xml"], "responses": {"202": {"description": "Message Accepted", "schema": {"$ref": "#/definitions/BizTalkHttpReceive"}, "examples": {"text/xml": "<BizTalkHttpReceive>\r\n  <CorrelationToken TokenType=\"string\" />\r\n</BizTalkHttpReceive>"}}, "401": {"description": "Authentication credentials are missing or incorrect"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error"}}}}}, "definitions": {"EmployerContactDetailsIncomplete": {"type": "object", "properties": {"MessageIdentifier": {"type": "string"}, "MessageSource": {"$ref": "#/definitions/MessageSource"}, "EmployerDetails": {"$ref": "#/definitions/EmployerDetails"}}, "xml": {"namespace": "http://www.unisuper.com.au/services/acurity/1.0.0/"}, "example": "<EmployerContactDetailsIncomplete xmlns=\"http://www.unisuper.com.au/services/acurity/1.0.0/\">\r\n  <MessageIdentifier xmlns=\"\">string</MessageIdentifier>\r\n  <MessageSource xmlns=\"\">\r\n    <OriginatingSystem>string</OriginatingSystem>\r\n    <MessageTimeStamp>string</MessageTimeStamp>\r\n    <MessagePriority>string</MessagePriority>\r\n  </MessageSource>\r\n  <EmployerDetails xmlns=\"\">\r\n    <FundEmployerIdentifier>string</FundEmployerIdentifier>\r\n    <EmployerName>string</EmployerName>\r\n    <EmployerType>string</EmployerType>\r\n    <EmployerABN>string</EmployerABN>\r\n  </EmployerDetails>\r\n</EmployerContactDetailsIncomplete>"}, "MessageSource": {"type": "object", "properties": {"OriginatingSystem": {"type": "string"}, "MessageTimeStamp": {"type": "string"}, "MessagePriority": {"type": "string"}}}, "EmployerDetails": {"type": "object", "properties": {"FundEmployerIdentifier": {"type": "string"}, "EmployerName": {"type": "string"}, "EmployerType": {"type": "string"}, "EmployerABN": {"type": "string"}}}, "BizTalkHttpReceive": {"type": "object", "properties": {"CorrelationToken": {"type": "object", "properties": {"TokenType": {"type": "string", "xml": {"attribute": true}}}}}}}, "tags": [{"name": "notifications", "description": "Operations for receiving event notifications emitted by Acurity."}]}