{"basePath": "/v1", "host": "apiservices-dev.usmnpz.com.au", "info": {"title": "Swagger Specification 2.0. Host: apiservices-dev.usmnpz.com.au", "version": "2.0"}, "paths": {"/accounts/send-message": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/adviser/details": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/control-message": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/fee-deduction/": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/financial-adviser-authority": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/member-alias": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/notifications": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/status": {"get": {"responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/status/": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/upload-document": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/{param1}/retrieve-document-list": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/{param1}/retrieve-document/{param2}": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}, {"in": "path", "name": "param2", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/adviser-portal/{param1}/rollover-in": {"post": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/agents/chat-availability": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/agents/chat-availability-v2": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/change-notifications/lifecycle-notifications": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/agent": {"get": {"responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/alias": {"get": {"responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/eol-api/employer/{param1}/details": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}, "options": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/ignition//alias": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/ignition//investments": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/accounts/{param1}/details": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/accounts/{param1}/investments": {"put": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/alias": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/investments": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/members/{param1}/details": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/members/{param1}/journeys": {"post": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/ignition/members/{param1}/journeys/{param2}": {"put": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}, {"in": "path", "name": "param2", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/memberinteraction/bi-datafeed/trigger-functions": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/members/alias": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/contact/addresses": {"put": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/members/{param1}/contact/emails": {"put": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/accounts": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/accounts/{param1}/dashboard": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/accounts/{param1}/more": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/content/{param1}/retrieve": {"get": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/content/{param1}/select-account": {"post": {"parameters": [{"in": "path", "name": "param1", "required": true, "type": "string"}], "responses": {"default": {"description": "Response body is empty"}}}}, "/mobile-api/status": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/mol-api/status": {"get": {"responses": {"default": {"description": "Response body is empty"}}}, "options": {"responses": {"default": {"description": "Response body is empty"}}}}, "/okta-api/entitlement-details": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/okta-api/terms-and-conditions": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/records-management/documents/dwflA23199801": {"get": {"responses": {"default": {"description": "Response body is empty"}}}}, "/records-management/documents/search": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/salesforce-crm/appointments": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/salesforce-crm/send": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/salesforce-event-processor/notifications/trigger": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}, "/security/users/id-verification/notify": {"post": {"responses": {"default": {"description": "Response body is empty"}}}}}, "schemes": ["http"], "swagger": "2.0"}