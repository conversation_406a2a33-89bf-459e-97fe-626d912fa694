{"swagger": "2.0", "info": {"title": "Calculator-Proxy-API-v1", "version": "1.0", "description": "Calculator Proxy API"}, "host": "apiservices-rgr.usmnpz.com.au", "basePath": "/v1/proxy/calculator", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/savings": {"post": {"description": "Fetches the details for the retirement savings calculator", "operationId": "calculateRetirementSavings", "summary": "Fetches the details for the retirement savings calculator", "tags": ["CalculateRetirementSavings"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - string. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "string"}, {"name": "retirementSavingsRequest", "in": "body", "schema": {"$ref": "#/definitions/RetirementSavingsRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Success Response from Retirement API", "schema": {"$ref": "#/definitions/RetirementSavingsResponse"}, "examples": {"application/json": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error or Unhandled Exception", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "503": {"description": "Service Unavailable"}}}}, "/content": {"get": {"description": "This API is responsible the content required for the calculator", "operationId": "RetrieveCalculator<PERSON>ontent", "summary": "Retrieve calculator content", "tags": ["RetrieveCalculator<PERSON>ontent"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - uuid. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "uuid"}], "produces": ["application/json"], "responses": {"200": {"description": "Request Succeeded", "schema": {"$ref": "#/definitions/CalculatorContentResponse"}, "examples": {"application/json": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error or Unhandled Exception", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "503": {"description": "Service Unavailable"}}}}, "/income": {"post": {"description": "Fetches the details for the retirement income calculator", "operationId": "CalculateRetirementIncome", "summary": "Fetches the details for the retirement income calculator", "tags": ["CalculateRetirementIncome"], "parameters": [{"name": "X-CorrelationId", "in": "header", "description": "Format - string. Client-generated GUID with no decoration such as curly braces (eg. '9C4D50EE-2D56-4CD3-8152-34347DC9F2B0').", "required": true, "type": "string", "format": "string"}, {"name": "retirementIncomeRequest", "in": "body", "schema": {"$ref": "#/definitions/RetirementIncomeRequest"}}], "consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "Success Response from Retirement API", "schema": {"$ref": "#/definitions/RetirementIncomeResponse"}, "examples": {"application/json": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden. The request is understood, but it has been refused or access is not allowed"}, "429": {"description": "The request cannot be served due to the rate limit having been exhausted for the resource."}, "500": {"description": "Internal Server Error or Unhandled Exception", "schema": {"type": "array", "items": {"$ref": "#/definitions/ErrorResponseItem"}, "x-content-type": "application/json"}, "examples": {"application/json": [{"code": "string", "message": "string"}]}}, "503": {"description": "Service Unavailable"}}}}}, "definitions": {"CalculatorContentResponse": {"type": "string"}, "RetirementSavingsRequest": {"type": "string", "example": "string"}, "RetirementSavingsResponse": {"type": "string"}, "RetirementIncomeRequest": {"type": "string", "example": "string"}, "RetirementIncomeResponse": {"type": "string"}, "ErrorResponseItem": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}}, "tags": [{"name": "CalculatorProxy API", "description": "CalculatorProxy API"}]}