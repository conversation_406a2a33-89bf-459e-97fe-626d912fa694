# environments/regression/regression.tfvars

api_security_sites = {
  "********" = {  # Key is the site_id for apiservices-rgr
    domain                           = "apiservices-rgr.usmnpz.com.au"
    is_api_only_site = false
    non_api_request_violation_action = "ALERT_ONLY"
    invalid_url_violation_action     = "ALERT_ONLY"
    invalid_method_violation_action  = "ALERT_ONLY"
    missing_param_violation_action   = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
    is_automatic_discovery_api_integration_enabled = false
    # invalid_param_name_violation_action = "ALERT_ONLY" # Not supported by provider
  }
}

# This map defines the specific API-level policies for those sites.
api_security_api_configs = {
  # --- Policies for apiservices-rgr ---
  "apiservices_rgr_accounts" = {
    site_id                           = "********"
    spec_file_path                     = "api-specs-reg/open-api-v2-v1-accounts-apiservices-rgr.usmnpz.com.au-100012.json"
    description                        = "API for managing member account details."
    base_path                          = "/v1/accounts"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
  "apiservices_rgr_acurity_notifications" = {
    site_id                           = "********"
    spec_file_path                     = "api-specs-reg/openapiv-v2-v1-acurity-notifications-apiservices-rgr.usmnpz.com.au-100013.json"
    description                        = "An API for handling member events emitted by Acurity."
    base_path                          = "/v1/acurity-notifications"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
  "apiservices_rgr_calculator" = {
    site_id                           = "********"
    spec_file_path                     = "api-specs-reg/openapi-v2-Calculator-Proxy-API-v1-apiservices-rgr.usmnpz.com.au-100014.json"
    description                        = "Calculator Proxy API"
    base_path                          = "/v1/proxy/calculator"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
   "apiservices_rgr_communication" = {
    site_id                           = "********"
    spec_file_path                     = "api-specs-reg/openapi-v2-v1-communication-apiservices-rgr.usmnpz.com.au-100015.json"
    description                        = "API for rendering and transmitting outbound communication (email/SMS) to members and employers."
    base_path                          = "/v1/communication"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  },
  "apiservices_rgr_document" = {
    site_id                           = "********"
    spec_file_path                     = "api-specs-reg/openapi-v2-v1-document-apiservices-rgr.usmnpz.com.au-100016.json"
    description                        = "API for storing and retrieving documents from Objective document store."
    base_path                          = "/v1/document"
    invalid_url_violation_action       = "ALERT_ONLY"
    invalid_method_violation_action    = "ALERT_ONLY"
    missing_param_violation_action     = "ALERT_ONLY"
    invalid_param_value_violation_action = "ALERT_ONLY"
  }
}