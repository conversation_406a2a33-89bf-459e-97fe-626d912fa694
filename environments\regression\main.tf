# main.tf

# TODO: This resource for creating the site - for future use
# resource "incapsula_site" "site" {
#   for_each   = var.api_security_sites
#   domain     = each.value.domain
#   account_id = var.imperva_account_id
# }

# This resource for site-level config
resource "incapsula_api_security_site_config" "site_config" {
  for_each = var.api_security_sites

  site_id = each.key

  is_api_only_site = each.value.is_api_only_site
  non_api_request_violation_action = each.value.non_api_request_violation_action
  is_automatic_discovery_api_integration_enabled = each.value.is_automatic_discovery_api_integration_enabled 
  invalid_url_violation_action               = each.value.invalid_url_violation_action
  invalid_method_violation_action            = each.value.invalid_method_violation_action
  missing_param_violation_action             = each.value.missing_param_violation_action
  invalid_param_value_violation_action       = each.value.invalid_param_value_violation_action
  # invalid_param_name_violation_action = each.value.invalid_param_name_violation_action # Not supported by provider
}

resource "incapsula_api_security_api_config" "api_config" {
  for_each = var.api_security_api_configs

  site_id           = each.value.site_id
  api_specification = file(each.value.spec_file_path)

  description                        = each.value.description
  base_path                          = each.value.base_path
  invalid_url_violation_action       = each.value.invalid_url_violation_action
  invalid_method_violation_action    = each.value.invalid_method_violation_action
  missing_param_violation_action     = each.value.missing_param_violation_action
  invalid_param_value_violation_action = each.value.invalid_param_value_violation_action
}