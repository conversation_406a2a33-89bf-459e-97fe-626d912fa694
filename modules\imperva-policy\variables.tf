# variables.tf

variable "imperva_api_id" {
  description = "The API ID for the Imperva account."
  type        = string
}

variable "imperva_api_key" {
  description = "The API Key for the Imperva account."
  type        = string
  sensitive   = true
}

variable "imperva_account_id" {
  description = "The numeric ID of the Imperva account to manage resources in."
  type        = string
}

# site-level config 
variable "api_security_sites" {
  description = "A map of sites to configure for site-level API security, keyed by site_id."
  type = map(object({
    is_automatic_discovery_api_integration_enabled = optional(bool, false)
    is_api_only_site                           = optional(bool, false)
    non_api_request_violation_action           = optional(string, "ALERT_ONLY")
    invalid_url_violation_action               = optional(string, "ALERT_ONLY")
    invalid_method_violation_action            = optional(string, "ALERT_ONLY")
    missing_param_violation_action             = optional(string, "ALERT_ONLY")
    invalid_param_value_violation_action       = optional(string, "ALERT_ONLY")
  }))
  default = {}
}

# API-level configs 
variable "api_security_api_configs" {
  description = "A map of specific API endpoints to configure from a specification file."
  type = map(object({
    site_id                            = string
    spec_file_path                     = string 
    description                        = optional(string)
    base_path                          = optional(string)
    
    # Mapping to the supported violation actions
    invalid_url_violation_action       = string
    invalid_method_violation_action    = string
    missing_param_violation_action     = string
    invalid_param_value_violation_action = string
    
    # NOTE: invalid_param_name_violation_action (for "New Parameters") is omitted as it is not supported by the provider.
  }))
  default = {}
}