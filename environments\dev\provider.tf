# environments/dev/providers.tf

terraform {
  required_version = ">= 1.5.0"

  cloud {
    organization = "unisuper"

    workspaces {
      name = "imperva-apiservices-dev" # Workspace name for this environment
    }
  }

  required_providers {
    incapsula = {
      source  = "imperva/incapsula"
      version = "3.34.3"
    }
  }
}

provider "incapsula" {
  api_id  = var.imperva_api_id
  api_key = var.imperva_api_key
}