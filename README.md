# DevSecOps: Imperva Policy Automation

This sub-project automates Imperva API Security management using Terraform and Infrastructure as Code (IaC) principles.

## Overview

Automated provisioning and management of Imperva API Security policies through the Incapsula Provider, ensuring consistent and version-controlled security configurations.

## Implementation

**Our implementation leverages:**
- **Terraform Cloud** for state management and collaboration
- **Imperva Incapsula Provider** for web application security management
- **Infrastructure as Code** principles for security policy management

## 🔐 Prerequisites

1. **Terraform Cloud Account**: https://app.terraform.io/app/unisuper/workspaces/devsecops-imperva-policy
2. **Imperva Credentials**:
    - API ID (`imperva_api_id`)
    - API Key (`imperva_api_key`)
    - Account ID (`imperva_account_id`)
3. **Azure DevOps Repo**: Connected to Terraform Cloud

## 🛠 Setup

### 1. Terraform Cloud Workspace
Set environment variables in TFC workspace:
```plaintext
imperva_api_id = "impervar api id"
imperva_api_key = "imperva api key"
imperva_account_id = "imperva account id"
```

### 2. Local Development Setup

1. Clone this repository and navigate to the project:
   ```bash
   git clone <repository-url>
   cd DevSecOps/devsecops-imperva-policy
   ```

2. Initialize Terraform configuration:
   ```bash
   terraform init
   ```

3. Configure local environment variables (for testing):
   ```bash
   export  var.imperva_api_id="API_ID"
   export var.imperva_api_key="API_KEY"
   export var.imperva_account_id="ACCOUNT_ID"

   ```

## Configuration Management

The Terraform configuration uses the [Incapsula Provider](https://registry.terraform.io/providers/imperva/incapsula/latest/docs) to manage:

- **Site Security Policies**: WAF rules and configurations
- **SSL Certificates**: Certificate management and validation
- **Access Control Rules**: IP whitelisting/blacklisting and geo-blocking
- **DDoS Protection Settings**: Rate limiting and traffic filtering
- **Custom Rules**: Application-specific security policies

## 🚀 Deployment Workflow

### Terraform Cloud (Production)
```bash
# Changes are automatically planned/applied via Terraform Cloud
# when code is pushed to the main branch
git add .
git commit -m "Update Imperva policies"
git push origin main
```

### Local Testing
```bash
# Initialize Terraform
terraform init

# Plan infrastructure changes
terraform plan

# Apply changes (for testing only)
terraform apply
```

## 📚 Documentation References

- [Confluence -  Imperva API Security Automation](https://unisuper.atlassian.net.mcas.ms/wiki/spaces/ifnet/pages/**********/Imperva+API+Security+Automation)

- [Incapsula Provider Documentation](https://registry.terraform.io/providers/imperva/incapsula/latest/docs)
- [Imperva Cloud Security Platform](https://www.imperva.com/products/web-application-firewall-waf/)
- [Terraform Cloud Documentation](https://developer.hashicorp.com/terraform/cloud-docs)

